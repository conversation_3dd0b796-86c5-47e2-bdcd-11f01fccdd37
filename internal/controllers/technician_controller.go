package controllers

import (
	"log"
	"net/http"
	"strconv"
	"time"

	"tradicao/internal/models"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
)

type TechnicianController struct {
	service      services.TechnicianService
	orderService services.OrderService
	authService  services.AuthService
	wsConfig     *WebSocketConfig
}

type WebSocketConfig struct {
	URL    string
	APIKey string
}

func NewTechnicianController(service services.TechnicianService, orderService services.OrderService, authService services.AuthService, wsConfig *WebSocketConfig) *TechnicianController {
	return &TechnicianController{
		service:      service,
		orderService: orderService,
		authService:  authService,
		wsConfig:     wsConfig,
	}
}

// @Summary Criar especialidade
// @Description Cria uma nova especialidade para técnicos
// @Tags technician
// @Accept json
// @Produce json
// @Param specialty body models.TechnicianSpecialty true "Dados da especialidade"
// @Success 201 {object} models.TechnicianSpecialty
// @Router /api/technician/specialties [post]
func (c *TechnicianController) CreateSpecialty(ctx *gin.Context) {
	var specialty models.TechnicianSpecialty
	if err := ctx.ShouldBindJSON(&specialty); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := c.service.CreateSpecialty(&specialty); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, specialty)
}

// @Summary Listar especialidades
// @Description Lista todas as especialidades de técnicos
// @Tags technician
// @Produce json
// @Success 200 {array} models.TechnicianSpecialty
// @Router /api/technician/specialties [get]
func (c *TechnicianController) ListSpecialties(ctx *gin.Context) {
	specialties, err := c.service.ListSpecialties()
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, specialties)
}

// UpdateSpecialty atualiza uma especialidade existente
func (c *TechnicianController) UpdateSpecialty(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	var specialty models.TechnicianSpecialty
	if err := ctx.ShouldBindJSON(&specialty); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	specialty.ID = uint(id)
	if err := c.service.UpdateSpecialty(&specialty); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, specialty)
}

// DeleteSpecialty remove uma especialidade
func (c *TechnicianController) DeleteSpecialty(ctx *gin.Context) {
	id, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	if err := c.service.DeleteSpecialty(uint(id)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

// @Summary Vincular técnico a filial
// @Description Vincula um técnico a uma filial com uma especialidade
// @Tags technician
// @Accept json
// @Produce json
// @Param association body models.TechnicianBranch true "Dados da vinculação"
// @Success 201 {object} models.TechnicianBranch
// @Router /api/technician/branches [post]
func (c *TechnicianController) CreateBranchAssociation(ctx *gin.Context) {
	var association models.TechnicianBranch
	if err := ctx.ShouldBindJSON(&association); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := c.service.CreateBranchAssociation(&association); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, association)
}

// @Summary Listar vinculações do técnico
// @Description Lista todas as vinculações de um técnico com filiais
// @Tags technician
// @Produce json
// @Param technician_id path int true "ID do técnico"
// @Success 200 {array} models.TechnicianBranch
// @Router /api/technician/{technician_id}/branches [get]
func (c *TechnicianController) ListBranchAssociations(ctx *gin.Context) {
	technicianID, err := strconv.ParseUint(ctx.Param("technician_id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	associations, err := c.service.ListBranchAssociations(uint(technicianID))
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, associations)
}

// DeleteBranchAssociation remove uma associação
func (c *TechnicianController) DeleteBranchAssociation(ctx *gin.Context) {
	technicianID, err := strconv.ParseUint(ctx.Param("technician_id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID do técnico inválido"})
		return
	}

	branchID, err := strconv.ParseUint(ctx.Param("branch_id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da filial inválido"})
		return
	}

	if err := c.service.DeleteBranchAssociation(uint(technicianID), uint(branchID)); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.Status(http.StatusNoContent)
}

// @Summary Criar histórico de manutenção
// @Description Registra um novo histórico de manutenção
// @Tags technician
// @Accept json
// @Produce json
// @Param history body models.MaintenanceHistory true "Dados do histórico"
// @Success 201 {object} models.MaintenanceHistory
// @Router /api/technician/history [post]
func (c *TechnicianController) CreateMaintenanceHistory(ctx *gin.Context) {
	var history models.MaintenanceHistory
	if err := ctx.ShouldBindJSON(&history); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	if err := c.service.CreateMaintenanceHistory(&history); err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusCreated, history)
}

// @Summary Listar histórico de manutenção
// @Description Lista o histórico de manutenções de um técnico
// @Tags technician
// @Produce json
// @Param technician_id path int true "ID do técnico"
// @Param start_date query string true "Data inicial (YYYY-MM-DD)"
// @Param end_date query string true "Data final (YYYY-MM-DD)"
// @Success 200 {array} models.MaintenanceHistory
// @Router /api/technician/{technician_id}/history [get]
func (c *TechnicianController) ListMaintenanceHistory(ctx *gin.Context) {
	technicianID, err := strconv.ParseUint(ctx.Param("technician_id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID inválido"})
		return
	}

	startDate, err := time.Parse("2006-01-02", ctx.Query("start_date"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Data inicial inválida"})
		return
	}

	endDate, err := time.Parse("2006-01-02", ctx.Query("end_date"))
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "Data final inválida"})
		return
	}

	history, err := c.service.ListMaintenanceHistory(uint(technicianID), startDate, endDate)
	if err != nil {
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": err.Error()})
		return
	}

	ctx.JSON(http.StatusOK, history)
}

// ShowOrderDetailFlip renderiza a página flip-card para detalhes de ordens do técnico
func (c *TechnicianController) ShowOrderDetailFlip(ctx *gin.Context) {
	// Verificar autenticação
	userID, exists := ctx.Get("user_id")
	if !exists {
		log.Printf("Tentativa de acesso não autorizado à página flip-card")
		ctx.Redirect(http.StatusFound, "/login")
		return
	}

	// Verificar se é técnico
	userRole, exists := ctx.Get("user_role")
	if !exists || userRole != "technician" {
		log.Printf("Usuário %v tentou acessar página de técnico sem permissão", userID)
		ctx.HTML(http.StatusForbidden, "error/403.html", gin.H{
			"message": "Acesso negado. Apenas técnicos podem acessar esta página.",
		})
		return
	}

	technicianID := userID.(uint)

	// Buscar dados do técnico
	technician, err := c.service.GetTechnicianByUserID(technicianID)
	if err != nil {
		log.Printf("Erro ao buscar dados do técnico %d: %v", technicianID, err)
		ctx.HTML(http.StatusInternalServerError, "error/500.html", gin.H{
			"message": "Erro interno do servidor",
		})
		return
	}

	// Buscar ordens atribuídas ao técnico com paginação
	page := 1
	if pageStr := ctx.Query("page"); pageStr != "" {
		if p, err := strconv.Atoi(pageStr); err == nil && p > 0 {
			page = p
		}
	}

	orders, totalCount, err := c.orderService.GetOrdersByTechnician(technicianID, page, 10)
	if err != nil {
		log.Printf("Erro ao buscar ordens do técnico %d: %v", technicianID, err)
		// Continuar com lista vazia em caso de erro
		orders = []models.MaintenanceOrder{}
		totalCount = 0
	}

	// Buscar filiais vinculadas ao técnico
	branchAssociations, err := c.service.ListBranchAssociations(technicianID)
	if err != nil {
		log.Printf("Erro ao buscar vinculações do técnico %d: %v", technicianID, err)
		branchAssociations = []models.TechnicianBranch{}
	}

	// Preparar dados para o template
	templateData := gin.H{
		"technician":         technician,
		"orders":            orders,
		"totalOrders":       totalCount,
		"currentPage":       page,
		"branchAssociations": branchAssociations,
		"wsConfig": gin.H{
			"url":    c.wsConfig.URL,
			"apiKey": c.wsConfig.APIKey,
		},
		"apiToken": ctx.GetHeader("Authorization"),
		"cache": gin.H{
			"ttl":     300, // 5 minutos
			"version": time.Now().Unix(),
		},
	}

	ctx.HTML(http.StatusOK, "tecnico/ordem_detail_flip.html", templateData)
}

// ShowGaleria renderiza a página de galeria atualizada para técnicos
func (c *TechnicianController) ShowGaleria(ctx *gin.Context) {
	// Verificar autenticação
	userID, exists := ctx.Get("user_id")
	if !exists {
		log.Printf("Tentativa de acesso não autorizado à galeria de técnico")
		ctx.Redirect(http.StatusFound, "/login")
		return
	}

	// Verificar se é técnico
	userRole, exists := ctx.Get("user_role")
	if !exists || userRole != "technician" {
		log.Printf("Usuário %v tentou acessar galeria de técnico sem permissão", userID)
		ctx.HTML(http.StatusForbidden, "error/403.html", gin.H{
			"message": "Acesso negado. Apenas técnicos podem acessar esta página.",
		})
		return
	}

	technicianID := userID.(uint)

	// Buscar dados do técnico com cache
	technician, err := c.service.GetTechnicianByUserID(technicianID)
	if err != nil {
		log.Printf("Erro ao buscar dados do técnico %d: %v", technicianID, err)
		ctx.HTML(http.StatusInternalServerError, "error/500.html", gin.H{
			"message": "Erro interno do servidor",
		})
		return
	}

	// Buscar filiais vinculadas ao técnico
	branchAssociations, err := c.service.ListBranchAssociations(technicianID)
	if err != nil {
		log.Printf("Erro ao buscar vinculações do técnico %d: %v", technicianID, err)
		branchAssociations = []models.TechnicianBranch{}
	}

	// Buscar ordens pendentes/em andamento do técnico
	pendingOrders, _, err := c.orderService.GetPendingOrdersByTechnician(technicianID, 1, 5)
	if err != nil {
		log.Printf("Erro ao buscar ordens pendentes do técnico %d: %v", technicianID, err)
		pendingOrders = []models.MaintenanceOrder{}
	}

	// Contar ordens por status para dashboard
	orderStats, err := c.orderService.GetOrderStatsByTechnician(technicianID)
	if err != nil {
		log.Printf("Erro ao buscar estatísticas de ordens do técnico %d: %v", technicianID, err)
		orderStats = map[string]int{}
	}

	// Preparar dados para o template
	templateData := gin.H{
		"technician":         technician,
		"branchAssociations": branchAssociations,
		"pendingOrders":      pendingOrders,
		"orderStats":         orderStats,
		"wsConfig": gin.H{
			"url":    c.wsConfig.URL,
			"apiKey": c.wsConfig.APIKey,
		},
		"apiToken": ctx.GetHeader("Authorization"),
		"cache": gin.H{
			"ttl":     300, // 5 minutos
			"version": time.Now().Unix(),
		},
		"notifications": gin.H{
			"enabled": true,
			"sound":   true,
		},
	}

	ctx.HTML(http.StatusOK, "galeria/galeria_tecnico_new.html", templateData)
}

// GetTechnicianDashboardData retorna dados do dashboard do técnico via API
func (c *TechnicianController) GetTechnicianDashboardData(ctx *gin.Context) {
	// Verificar autenticação
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Não autenticado"})
		return
	}

	// Verificar se é técnico
	userRole, exists := ctx.Get("user_role")
	if !exists || userRole != "technician" {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Acesso negado"})
		return
	}

	technicianID := userID.(uint)

	// Buscar dados do dashboard
	dashboardData, err := c.orderService.GetTechnicianDashboardData(technicianID)
	if err != nil {
		log.Printf("Erro ao buscar dados do dashboard do técnico %d: %v", technicianID, err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro interno do servidor"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"data":      dashboardData,
		"timestamp": time.Now().Unix(),
		"cache_ttl": 300,
	})
}

// UpdateOrderStatus atualiza o status de uma ordem (endpoint específico para técnicos)
func (c *TechnicianController) UpdateOrderStatus(ctx *gin.Context) {
	// Verificar autenticação
	userID, exists := ctx.Get("user_id")
	if !exists {
		ctx.JSON(http.StatusUnauthorized, gin.H{"error": "Não autenticado"})
		return
	}

	// Verificar se é técnico
	userRole, exists := ctx.Get("user_role")
	if !exists || userRole != "technician" {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Acesso negado"})
		return
	}

	// Obter ID da ordem
	orderID, err := strconv.ParseUint(ctx.Param("id"), 10, 32)
	if err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": "ID da ordem inválido"})
		return
	}

	// Verificar se a ordem está atribuída ao técnico
	technicianID := userID.(uint)
	isAssigned, err := c.orderService.IsOrderAssignedToTechnician(uint(orderID), technicianID)
	if err != nil {
		log.Printf("Erro ao verificar atribuição da ordem %d ao técnico %d: %v", orderID, technicianID, err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro interno do servidor"})
		return
	}

	if !isAssigned {
		ctx.JSON(http.StatusForbidden, gin.H{"error": "Ordem não atribuída a este técnico"})
		return
	}

	// Obter novo status
	var statusUpdate struct {
		Status string `json:"status" binding:"required"`
		Notes  string `json:"notes"`
	}

	if err := ctx.ShouldBindJSON(&statusUpdate); err != nil {
		ctx.JSON(http.StatusBadRequest, gin.H{"error": err.Error()})
		return
	}

	// Atualizar status
	err = c.orderService.UpdateOrderStatusByTechnician(uint(orderID), technicianID, statusUpdate.Status, statusUpdate.Notes)
	if err != nil {
		log.Printf("Erro ao atualizar status da ordem %d pelo técnico %d: %v", orderID, technicianID, err)
		ctx.JSON(http.StatusInternalServerError, gin.H{"error": "Erro ao atualizar status"})
		return
	}

	ctx.JSON(http.StatusOK, gin.H{
		"message":   "Status atualizado com sucesso",
		"timestamp": time.Now().Unix(),
	})
}
