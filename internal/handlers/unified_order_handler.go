package handlers

import (
	"context"
	"fmt"
	"log"
	"net/http"
	"strconv"
	"time"

	"tradicao/internal/database"
	"tradicao/internal/models"
	"tradicao/internal/repository"
	"tradicao/internal/services"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UnifiedOrderHandler - Handler unificado para todas as operações de ordens
type UnifiedOrderHandler struct {
	repo               *repository.MaintenanceOrderRepository
	service            *services.MaintenanceOrderService
	notificationService *services.NotificationService
}

// NewUnifiedOrderHandler cria uma nova instância do handler unificado
func NewUnifiedOrderHandler() *UnifiedOrderHandler {
	db, err := database.Connect()
	if err != nil {
		return nil
	}

	repo := repository.NewMaintenanceOrderRepository(db)

	// Obter conexão SQL
	sqlDB, err := db.DB()
	if err != nil {
		log.Printf("Erro ao obter conexão SQL: %v", err)
		return nil
	}

	service := services.NewMaintenanceOrderService(repo, sqlDB)
	
	// Inicializar serviço de notificações
	notificationService := services.NewNotificationService(db)

	return &UnifiedOrderHandler{
		repo:               repo,
		service:           service,
		notificationService: notificationService,
	}
}

// StandardResponse - Resposta padronizada para todas as APIs
type StandardResponse struct {
	Success bool        `json:"success"`
	Message string      `json:"message"`
	Data    interface{} `json:"data,omitempty"`
	Meta    interface{} `json:"meta,omitempty"`
	Error   string      `json:"error,omitempty"`
}

// OrderFilters - Filtros padronizados para ordens
type OrderFilters struct {
	Status       string `json:"status"`
	BranchID     uint   `json:"branch_id"`
	TechnicianID uint   `json:"technician_id"`
	ProviderID   uint   `json:"provider_id"`
	StartDate    string `json:"start_date"`
	EndDate      string `json:"end_date"`
	Priority     string `json:"priority"`
	Search       string `json:"search"`
	ExcludeTest  bool   `json:"exclude_test"`
}

// UpdateStatusRequest - Estrutura para atualização de status
type UpdateStatusRequest struct {
	Status string `json:"status" binding:"required"`
	Notes  string `json:"notes,omitempty"`
}

// AssignOrderRequest - Estrutura para atribuição de ordem
type AssignOrderRequest struct {
	TechnicianID *uint `json:"technician_id,omitempty"`
	ProviderID   *uint `json:"provider_id,omitempty"`
	Notes        string `json:"notes,omitempty"`
}

// ValidOrderStatuses - Status válidos para ordens
var ValidOrderStatuses = map[string]bool{
	"pending":     true,
	"assigned":    true,
	"in_progress": true,
	"completed":   true,
	"cancelled":   true,
	"on_hold":     true,
}

// StatusTransitions - Transições válidas de status
var StatusTransitions = map[string][]string{
	"pending":     {"assigned", "cancelled"},
	"assigned":    {"in_progress", "cancelled", "on_hold"},
	"in_progress": {"completed", "on_hold", "cancelled"},
	"on_hold":     {"assigned", "in_progress", "cancelled"},
	"completed":   {}, // Status final
	"cancelled":   {}, // Status final
}

// PaginationMeta - Metadados de paginação
type PaginationMeta struct {
	Page       int   `json:"page"`
	PageSize   int   `json:"page_size"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
}

// ListOrders - Endpoint unificado para listar ordens
// GET /api/orders
func (h *UnifiedOrderHandler) ListOrders(c *gin.Context) {
	// Extrair parâmetros de paginação
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	pageSize, _ := strconv.Atoi(c.DefaultQuery("page_size", "10"))

	// Validar paginação
	if page < 1 {
		page = 1
	}
	if pageSize < 1 || pageSize > 100 {
		pageSize = 10
	}

	// Extrair filtros
	filters := h.extractFilters(c)

	// Obter informações do usuário
	userID, userRole := h.getUserInfo(c)

	// Buscar ordens com filtros otimizados
	orders, total, err := h.getOrdersWithOptimizedQuery(c, filters, userID, userRole, page, pageSize)
	if err != nil {
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar ordens",
			Error:   err.Error(),
		})
		return
	}

	// Calcular metadados de paginação
	totalPages := int((total + int64(pageSize) - 1) / int64(pageSize))

	meta := PaginationMeta{
		Page:       page,
		PageSize:   pageSize,
		Total:      total,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: fmt.Sprintf("Encontradas %d ordens", total),
		Data:    orders,
		Meta:    meta,
	})
}

// GetOrder - Endpoint unificado para obter uma ordem específica
// GET /api/orders/:id
func (h *UnifiedOrderHandler) GetOrder(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "ID inválido",
			Error:   "ID deve ser um número válido",
		})
		return
	}

	// Verificar se é a ordem problemática #18
	if id == 18 {
		c.JSON(http.StatusForbidden, StandardResponse{
			Success: false,
			Message: "Ordem não disponível",
			Error:   "Esta ordem não está disponível para visualização",
		})
		return
	}

	// Obter informações do usuário
	userID, userRole := h.getUserInfo(c)

	// Buscar ordem com dados relacionados
	order, err := h.getOrderWithRelations(c, uint(id), userID, userRole)
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, StandardResponse{
				Success: false,
				Message: "Ordem não encontrada",
			})
			return
		}

		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar ordem",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: "Ordem encontrada",
		Data:    order,
	})
}

// GetCalendarOrders - Endpoint unificado para ordens do calendário
// GET /api/orders/calendar
func (h *UnifiedOrderHandler) GetCalendarOrders(c *gin.Context) {
	// Obter parâmetros de data
	monthStr := c.DefaultQuery("month", strconv.Itoa(int(time.Now().Month())))
	yearStr := c.DefaultQuery("year", strconv.Itoa(time.Now().Year()))

	month, err := strconv.Atoi(monthStr)
	if err != nil || month < 1 || month > 12 {
		month = int(time.Now().Month())
	}

	year, err := strconv.Atoi(yearStr)
	if err != nil || year < 2000 || year > 2100 {
		year = time.Now().Year()
	}

	// Extrair filtros
	filters := h.extractFilters(c)

	// Obter informações do usuário
	userID, userRole := h.getUserInfo(c)

	// Calcular range de datas
	startDate := time.Date(year, time.Month(month), 1, 0, 0, 0, 0, time.Local)
	endDate := startDate.AddDate(0, 1, 0).Add(-time.Second)

	// Buscar ordens do mês
	orders, err := h.getCalendarOrders(c, startDate, endDate, filters, userID, userRole)
	if err != nil {
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar ordens do calendário",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: fmt.Sprintf("Encontradas %d ordens para %s/%d", len(orders), monthStr, year),
		Data:    orders,
	})
}

// GetTechnicianOrders - Endpoint unificado para ordens do técnico
// GET /api/orders/technician
func (h *UnifiedOrderHandler) GetTechnicianOrders(c *gin.Context) {
	// Obter informações do usuário
	userID, userRole := h.getUserInfo(c)

	if userID == 0 {
		c.JSON(http.StatusUnauthorized, StandardResponse{
			Success: false,
			Message: "Usuário não autenticado",
		})
		return
	}

	// Extrair filtros
	filters := h.extractFilters(c)

	// Filtro específico por data se fornecido
	dateStr := c.Query("date")

	// Buscar ordens do técnico
	orders, err := h.getTechnicianOrders(c, userID, userRole, dateStr, filters)
	if err != nil {
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar ordens do técnico",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: fmt.Sprintf("Encontradas %d ordens", len(orders)),
		Data:    orders,
	})
}

// Endpoint simples para ordens do técnico: retorna todas as ordens onde technician_id = userID
func (h *UnifiedOrderHandler) GetTechnicianOrdersSimple(c *gin.Context) {
	userID, _ := h.getUserInfo(c)
	if userID == 0 {
		c.JSON(401, gin.H{"success": false, "message": "Usuário não autenticado"})
		return
	}
	db := h.repo.Db
	var ordens []map[string]interface{}
	db.Raw("SELECT * FROM maintenance_orders WHERE technician_id = ?", userID).Scan(&ordens)
	c.JSON(200, gin.H{"success": true, "data": ordens})
}

// CreateOrder - Endpoint unificado para criar ordens
// POST /api/orders
func (h *UnifiedOrderHandler) CreateOrder(c *gin.Context) {
	// Obter informações do usuário
	userID, _ := h.getUserInfo(c)

	if userID == 0 {
		c.JSON(http.StatusUnauthorized, StandardResponse{
			Success: false,
			Message: "Usuário não autenticado",
		})
		return
	}

	// Decodificar dados da requisição
	var orderData models.MaintenanceOrder
	if err := c.ShouldBindJSON(&orderData); err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Dados inválidos",
			Error:   err.Error(),
		})
		return
	}

	// Normalizar prioridade explicitamente após o bind
	orderData.Priority = models.NormalizePriorityLevel(string(orderData.Priority))

	// Validações básicas
	if orderData.Title == "" && orderData.Problem == "" {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Título ou problema é obrigatório",
		})
		return
	}

	if orderData.BranchID == 0 {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Filial é obrigatória",
		})
		return
	}

	if orderData.EquipmentID == 0 {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Equipamento é obrigatório",
		})
		return
	}

	// Configurar campos padrão
	now := time.Now()
	orderData.CreatedByUserID = userID
	orderData.UserID = userID
	orderData.Status = models.StatusPending
	orderData.OpenDate = now
	orderData.CreatedAt = now
	orderData.UpdatedAt = now

	// Se não tem título, usar o problema
	if orderData.Title == "" {
		orderData.Title = orderData.Problem
	}

	// Se não tem descrição, usar o problema
	if orderData.Description == "" {
		orderData.Description = orderData.Problem
	}

	// Criar a ordem
	err := h.service.CreateOrder(&orderData, int(userID))
	if err != nil {
		log.Printf("Erro ao criar ordem: %v", err)
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao criar ordem",
			Error:   err.Error(),
		})
		return
	}

	log.Printf("Ordem criada com sucesso: ID=%d, Usuário=%d", orderData.ID, userID)

	// Enviar notificação automática após criação
	go func() {
		if err := h.notificationService.NotifyOrderCreated(&orderData); err != nil {
			log.Printf("Erro ao enviar notificação de ordem criada: %v", err)
		}
	}()

	c.JSON(http.StatusCreated, StandardResponse{
		Success: true,
		Message: "Ordem criada com sucesso",
		Data:    orderData,
	})
}

// Métodos auxiliares

// extractFilters extrai filtros padronizados da requisição
func (h *UnifiedOrderHandler) extractFilters(c *gin.Context) OrderFilters {
	filters := OrderFilters{
		ExcludeTest: false, // REMOVIDO: coluna is_test não existe
	}

	// Status
	if status := c.Query("status"); status != "" && status != "all" {
		filters.Status = status
	}

	// Branch ID
	if branchIDStr := c.Query("branch_id"); branchIDStr != "" && branchIDStr != "all" {
		if branchID, err := strconv.ParseUint(branchIDStr, 10, 32); err == nil {
			filters.BranchID = uint(branchID)
		}
	}

	// Technician ID
	if techIDStr := c.Query("technician_id"); techIDStr != "" {
		if techID, err := strconv.ParseUint(techIDStr, 10, 32); err == nil {
			filters.TechnicianID = uint(techID)
		}
	}

	// Provider ID
	if providerIDStr := c.Query("provider_id"); providerIDStr != "" {
		if providerID, err := strconv.ParseUint(providerIDStr, 10, 32); err == nil {
			filters.ProviderID = uint(providerID)
		}
	}

	// Datas
	filters.StartDate = c.Query("start_date")
	filters.EndDate = c.Query("end_date")

	// Prioridade
	if priority := c.Query("priority"); priority != "" && priority != "all" {
		filters.Priority = priority
	}

	// Busca
	filters.Search = c.Query("search")

	return filters
}

// getUserInfo obtém informações do usuário do contexto
func (h *UnifiedOrderHandler) getUserInfo(c *gin.Context) (uint, string) {
	var userID uint
	var userRole string

	// Tentar obter userID de diferentes chaves possíveis
	if id, exists := c.Get("userID"); exists {
		if idUint, ok := id.(uint); ok {
			userID = idUint
		} else if idStr, ok := id.(string); ok {
			if parsed, err := strconv.ParseUint(idStr, 10, 32); err == nil {
				userID = uint(parsed)
			}
		}
	}

	// Tentar obter userRole
	if role, exists := c.Get("userRole"); exists {
		if roleStr, ok := role.(string); ok {
			userRole = roleStr
		}
	}

	return userID, userRole
}

// getOrdersWithOptimizedQuery busca ordens com query otimizada
func (h *UnifiedOrderHandler) getOrdersWithOptimizedQuery(ctx context.Context, filters OrderFilters, userID uint, userRole string, page, pageSize int) ([]models.MaintenanceOrderDetailed, int64, error) {
	// Usar o repositório existente com filtros padronizados
	repoFilters := make(map[string]interface{})

	// Aplicar filtros
	if filters.Status != "" {
		repoFilters["status"] = filters.Status
	}
	if filters.BranchID > 0 {
		repoFilters["branch_id"] = filters.BranchID
	}
	if filters.StartDate != "" {
		repoFilters["start_date"] = filters.StartDate
	}
	if filters.EndDate != "" {
		repoFilters["end_date"] = filters.EndDate
	}
	if filters.Priority != "" {
		repoFilters["priority"] = filters.Priority
	}
	if filters.Search != "" {
		repoFilters["search"] = filters.Search
	}

	// Buscar ordens usando o repositório (corrigindo assinatura)
	orders, total, err := h.repo.GetAll(ctx, repoFilters, int64(userID), userRole, page, pageSize)
	if err != nil {
		return nil, 0, err
	}

	return orders, int64(total), nil
}

// getOrderWithRelations busca uma ordem com todos os relacionamentos
func (h *UnifiedOrderHandler) getOrderWithRelations(ctx context.Context, orderID, userID uint, userRole string) (*models.MaintenanceOrderDetailed, error) {
	// Usar o repositório para buscar ordem específica (corrigindo assinatura)
	order, err := h.repo.GetByID(ctx, orderID)
	if err != nil {
		return nil, err
	}

	// Converter MaintenanceOrder para MaintenanceOrderDetailed
	detailed := &models.MaintenanceOrderDetailed{
		ID:            order.ID,
		OrderNumber:   order.Number,
		Title:         order.Title,
		Description:   order.Description,
		Status:        string(order.Status),
		Priority:      string(order.Priority),
		BranchID:      order.BranchID,
		BranchName:    order.BranchName,
		CreatedBy:     order.CreatedByUserID,
		CreatedByName: order.CreatedByName,
		AssignedTo:    order.TechnicianID,
		StartDate:     &order.OpenDate,
		CompletedDate: order.CompletionDate,
		CreatedAt:     order.CreatedAt,
		UpdatedAt:     order.UpdatedAt,
		TotalCost:     order.ActualCost,
	}

	return detailed, nil
}

// getCalendarOrders busca ordens para o calendário
func (h *UnifiedOrderHandler) getCalendarOrders(ctx context.Context, startDate, endDate time.Time, filters OrderFilters, userID uint, userRole string) ([]models.MaintenanceOrderDetailed, error) {
	// Criar filtros específicos para calendário
	repoFilters := map[string]interface{}{
		"start_date": startDate.Format("2006-01-02"),
		"end_date":   endDate.Format("2006-01-02"),
	}

	// Aplicar filtros adicionais
	if filters.BranchID > 0 {
		repoFilters["branch_id"] = filters.BranchID
	}
	if filters.Status != "" {
		repoFilters["status"] = filters.Status
	}

	// Buscar ordens do período (corrigindo assinatura)
	orders, _, err := h.repo.GetAll(ctx, repoFilters, int64(userID), userRole, 1, 1000) // Limite alto para calendário
	if err != nil {
		return nil, err
	}

	return orders, nil
}

// getTechnicianOrders busca ordens específicas do técnico
func (h *UnifiedOrderHandler) getTechnicianOrders(ctx context.Context, userID uint, userRole, dateFilter string, filters OrderFilters) ([]models.MaintenanceOrderDetailed, error) {
	// Criar filtros específicos para técnico
	repoFilters := map[string]interface{}{
		"technician_id": userID,
	}

	// Aplicar filtro de data se fornecido
	if dateFilter != "" {
		repoFilters["date"] = dateFilter
	}

	// Aplicar outros filtros
	if filters.Status != "" {
		repoFilters["status"] = filters.Status
	}

	// Buscar ordens do técnico (corrigindo assinatura)
	orders, _, err := h.repo.GetAll(ctx, repoFilters, int64(userID), userRole, 1, 200) // Limite razoável
	if err != nil {
		return nil, err
	}

	return orders, nil
}

// GetAvailableProvidersByEquipment retorna prestadoras disponíveis para uma filial e tipo de equipamento
// GET /api/orders/available-providers
func (h *UnifiedOrderHandler) GetAvailableProvidersByEquipment(c *gin.Context) {
	branchIDStr := c.Query("branch_id")
	equipmentTypeIDStr := c.Query("equipment_type_id")

	if branchIDStr == "" || equipmentTypeIDStr == "" {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Parâmetros obrigatórios",
			Error:   "branch_id e equipment_type_id são obrigatórios",
		})
		return
	}

	branchID, err := strconv.Atoi(branchIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Parâmetro inválido",
			Error:   "branch_id deve ser um número válido",
		})
		return
	}

	equipmentTypeID, err := strconv.Atoi(equipmentTypeIDStr)
	if err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Parâmetro inválido",
			Error:   "equipment_type_id deve ser um número válido",
		})
		return
	}

	providers, err := h.service.GetAvailableProviders(branchID, equipmentTypeID)
	if err != nil {
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar prestadoras",
			Error:   err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: fmt.Sprintf("Encontradas %d prestadoras disponíveis", len(providers)),
		Data:    providers,
	})
}

// GetAvailableTechniciansByEquipment retorna técnicos disponíveis para uma filial e tipo de equipamento
// GET /api/orders/available-technicians
//
// Utiliza vínculos separados: técnico deve estar vinculado à filial (technician_branches)
// E apto ao tipo de equipamento (technician_equipment_types)
func (h *UnifiedOrderHandler) GetAvailableTechniciansByEquipment(c *gin.Context) {
	branchIDStr := c.Query("branch_id")
	equipmentTypeIDStr := c.Query("equipment_type_id")

	log.Printf("[DEBUG] GetAvailableTechniciansByEquipment - Parâmetros recebidos: branch_id=%s, equipment_type_id=%s", branchIDStr, equipmentTypeIDStr)

	// Validar que ambos parâmetros são obrigatórios
	if branchIDStr == "" || equipmentTypeIDStr == "" {
		log.Printf("[ERROR] GetAvailableTechniciansByEquipment - Parâmetros obrigatórios ausentes")
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Parâmetros obrigatórios",
			Error:   "branch_id e equipment_type_id são obrigatórios para filtrar técnicos qualificados",
		})
		return
	}

	branchID, err := strconv.Atoi(branchIDStr)
	if err != nil || branchID <= 0 {
		log.Printf("[ERROR] GetAvailableTechniciansByEquipment - branch_id inválido: %s", branchIDStr)
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Parâmetro inválido",
			Error:   "branch_id deve ser um número válido maior que zero",
		})
		return
	}

	equipmentTypeID, err := strconv.Atoi(equipmentTypeIDStr)
	if err != nil || equipmentTypeID <= 0 {
		log.Printf("[ERROR] GetAvailableTechniciansByEquipment - equipment_type_id inválido: %s", equipmentTypeIDStr)
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Parâmetro inválido",
			Error:   "equipment_type_id deve ser um número válido maior que zero",
		})
		return
	}

	log.Printf("[DEBUG] GetAvailableTechniciansByEquipment - Usando vínculos separados: técnico-filial E técnico-tipo_equipamento")
	log.Printf("[DEBUG] GetAvailableTechniciansByEquipment - Filtros: branchID=%d, equipmentTypeID=%d", branchID, equipmentTypeID)

	// Chamar service que agora usa vínculos separados (technician_branches + technician_equipment_types)
	technicians, err := h.service.GetAvailableTechnicians(branchID, equipmentTypeID)
	if err != nil {
		log.Printf("[ERROR] GetAvailableTechniciansByEquipment - Erro no service com vínculos separados: %v", err)
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar técnicos qualificados",
			Error:   err.Error(),
		})
		return
	}

	log.Printf("[DEBUG] GetAvailableTechniciansByEquipment - Técnicos qualificados encontrados: %d", len(technicians))
	log.Printf("[DEBUG] GetAvailableTechniciansByEquipment - Critério: vinculados à filial %d E aptos ao tipo de equipamento %d", branchID, equipmentTypeID)

	for i, tech := range technicians {
		log.Printf("[DEBUG] GetAvailableTechniciansByEquipment - Técnico %d: ID=%d, Nome=%s, Email=%s", i+1, tech.ID, tech.Name, tech.Email)
	}

	// Manter mesmo formato de resposta JSON para compatibilidade com frontend
	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: fmt.Sprintf("Encontrados %d técnicos qualificados (vinculados à filial e aptos ao tipo de equipamento)", len(technicians)),
		Data:    technicians,
	})
}

// UpdateOrderStatus - Endpoint unificado para atualizar status de ordem
// PUT /api/orders/:id/status
func (h *UnifiedOrderHandler) UpdateOrderStatus(c *gin.Context) {
	// Obter ID da ordem
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "ID inválido",
			Error:   "ID deve ser um número válido",
		})
		return
	}

	// Verificar se é a ordem problemática #18
	if id == 18 {
		c.JSON(http.StatusForbidden, StandardResponse{
			Success: false,
			Message: "Ordem não disponível",
			Error:   "Esta ordem não está disponível para modificação",
		})
		return
	}

	// Obter informações do usuário
	userID, userRole := h.getUserInfo(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, StandardResponse{
			Success: false,
			Message: "Usuário não autenticado",
		})
		return
	}

	// Decodificar dados da requisição
	var updateRequest UpdateStatusRequest
	if err := c.ShouldBindJSON(&updateRequest); err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Dados inválidos",
			Error:   err.Error(),
		})
		return
	}

	// Validar status
	if !ValidOrderStatuses[updateRequest.Status] {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Status inválido",
			Error:   fmt.Sprintf("Status '%s' não é válido. Status válidos: pending, assigned, in_progress, completed, cancelled, on_hold", updateRequest.Status),
		})
		return
	}

	// Buscar ordem atual
	currentOrder, err := h.repo.GetByID(c, uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, StandardResponse{
				Success: false,
				Message: "Ordem não encontrada",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar ordem",
			Error:   err.Error(),
		})
		return
	}

	// Validar transição de status
	currentStatus := string(currentOrder.Status)
	if !h.isValidStatusTransition(currentStatus, updateRequest.Status) {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Transição de status inválida",
			Error:   fmt.Sprintf("Não é possível alterar status de '%s' para '%s'", currentStatus, updateRequest.Status),
		})
		return
	}

	// Verificar permissões
	if !h.canUpdateOrderStatus(currentOrder, userID, userRole) {
		c.JSON(http.StatusForbidden, StandardResponse{
			Success: false,
			Message: "Sem permissão para atualizar esta ordem",
		})
		return
	}

	// Atualizar status
	err = h.service.UpdateOrderStatus(uint(id), updateRequest.Status, updateRequest.Notes, userID)
	if err != nil {
		log.Printf("Erro ao atualizar status da ordem %d: %v", id, err)
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao atualizar status",
			Error:   err.Error(),
		})
		return
	}

	// Registrar log de auditoria
	log.Printf("Status da ordem %d alterado de '%s' para '%s' por usuário %d", id, currentStatus, updateRequest.Status, userID)

	// Enviar notificação automática
	go func() {
		if err := h.notificationService.NotifyStatusChanged(uint(id), currentStatus, updateRequest.Status, userID); err != nil {
			log.Printf("Erro ao enviar notificação de mudança de status: %v", err)
		}
	}()

	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: fmt.Sprintf("Status atualizado para '%s' com sucesso", updateRequest.Status),
	})
}

// AssignOrder - Endpoint unificado para atribuir ordem
// POST /api/orders/:id/assign
func (h *UnifiedOrderHandler) AssignOrder(c *gin.Context) {
	// Obter ID da ordem
	idStr := c.Param("id")
	id, err := strconv.ParseUint(idStr, 10, 32)
	if err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "ID inválido",
			Error:   "ID deve ser um número válido",
		})
		return
	}

	// Verificar se é a ordem problemática #18
	if id == 18 {
		c.JSON(http.StatusForbidden, StandardResponse{
			Success: false,
			Message: "Ordem não disponível",
			Error:   "Esta ordem não está disponível para atribuição",
		})
		return
	}

	// Obter informações do usuário
	userID, userRole := h.getUserInfo(c)
	if userID == 0 {
		c.JSON(http.StatusUnauthorized, StandardResponse{
			Success: false,
			Message: "Usuário não autenticado",
		})
		return
	}

	// Decodificar dados da requisição
	var assignRequest AssignOrderRequest
	if err := c.ShouldBindJSON(&assignRequest); err != nil {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Dados inválidos",
			Error:   err.Error(),
		})
		return
	}

	// Validar que apenas um tipo de atribuição foi fornecido
	if (assignRequest.TechnicianID != nil && assignRequest.ProviderID != nil) ||
		(assignRequest.TechnicianID == nil && assignRequest.ProviderID == nil) {
		c.JSON(http.StatusBadRequest, StandardResponse{
			Success: false,
			Message: "Atribuição inválida",
			Error:   "Deve ser fornecido apenas technician_id OU provider_id, não ambos",
		})
		return
	}

	// Buscar ordem atual
	currentOrder, err := h.repo.GetByID(c, uint(id))
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			c.JSON(http.StatusNotFound, StandardResponse{
				Success: false,
				Message: "Ordem não encontrada",
			})
			return
		}
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao buscar ordem",
			Error:   err.Error(),
		})
		return
	}

	// Verificar permissões
	if !h.canAssignOrder(currentOrder, userID, userRole) {
		c.JSON(http.StatusForbidden, StandardResponse{
			Success: false,
			Message: "Sem permissão para atribuir esta ordem",
		})
		return
	}

	// Validar vínculos se atribuindo a técnico
	if assignRequest.TechnicianID != nil {
		valid, err := h.service.ValidateTechnicianAssignment(*assignRequest.TechnicianID, currentOrder.BranchID, currentOrder.EquipmentID)
		if err != nil {
			c.JSON(http.StatusInternalServerError, StandardResponse{
				Success: false,
				Message: "Erro ao validar atribuição",
				Error:   err.Error(),
			})
			return
		}
		if !valid {
			c.JSON(http.StatusBadRequest, StandardResponse{
				Success: false,
				Message: "Técnico não qualificado",
				Error:   "Técnico não possui vínculo com a filial ou não está apto para este tipo de equipamento",
			})
			return
		}
	}

	// Realizar atribuição
	var assignedTo uint
	var assignedType string
	
	if assignRequest.TechnicianID != nil {
		assignedTo = *assignRequest.TechnicianID
		assignedType = "technician"
		err = h.service.AssignOrderToTechnician(uint(id), *assignRequest.TechnicianID, assignRequest.Notes, userID)
	} else {
		assignedTo = *assignRequest.ProviderID
		assignedType = "provider"
		err = h.service.AssignOrderToProvider(uint(id), *assignRequest.ProviderID, assignRequest.Notes, userID)
	}

	if err != nil {
		log.Printf("Erro ao atribuir ordem %d: %v", id, err)
		c.JSON(http.StatusInternalServerError, StandardResponse{
			Success: false,
			Message: "Erro ao atribuir ordem",
			Error:   err.Error(),
		})
		return
	}

	// Registrar log de auditoria
	log.Printf("Ordem %d atribuída a %s %d por usuário %d", id, assignedType, assignedTo, userID)

	// Enviar notificação automática
	go func() {
		if err := h.notificationService.NotifyOrderAssigned(uint(id), assignedTo, assignedType); err != nil {
			log.Printf("Erro ao enviar notificação de atribuição: %v", err)
		}
	}()

	c.JSON(http.StatusOK, StandardResponse{
		Success: true,
		Message: fmt.Sprintf("Ordem atribuída ao %s com sucesso", assignedType),
	})
}

// Métodos auxiliares para validação

// isValidStatusTransition verifica se a transição de status é válida
func (h *UnifiedOrderHandler) isValidStatusTransition(currentStatus, newStatus string) bool {
	if currentStatus == newStatus {
		return false // Não permitir transição para o mesmo status
	}
	
	validTransitions, exists := StatusTransitions[currentStatus]
	if !exists {
		return false
	}
	
	for _, validStatus := range validTransitions {
		if validStatus == newStatus {
			return true
		}
	}
	
	return false
}

// canUpdateOrderStatus verifica se o usuário pode atualizar o status da ordem
func (h *UnifiedOrderHandler) canUpdateOrderStatus(order *models.MaintenanceOrder, userID uint, userRole string) bool {
	// Admin pode atualizar qualquer ordem
	if userRole == "admin" {
		return true
	}
	
	// Usuário da filial pode atualizar ordens da sua filial
	if userRole == "filial" && order.BranchID > 0 {
		// Verificar se usuário pertence à filial (implementar conforme necessário)
		return true
	}
	
	// Técnico pode atualizar apenas ordens atribuídas a ele
	if userRole == "technician" && order.TechnicianID != nil && *order.TechnicianID == userID {
		return true
	}
	
	// Prestador pode atualizar apenas ordens atribuídas a ele
	if userRole == "provider" && order.ProviderID != nil && *order.ProviderID == userID {
		return true
	}
	
	return false
}

// canAssignOrder verifica se o usuário pode atribuir a ordem
func (h *UnifiedOrderHandler) canAssignOrder(order *models.MaintenanceOrder, userID uint, userRole string) bool {
	// Admin pode atribuir qualquer ordem
	if userRole == "admin" {
		return true
	}
	
	// Usuário da filial pode atribuir ordens da sua filial
	if userRole == "filial" && order.BranchID > 0 {
		// Verificar se usuário pertence à filial (implementar conforme necessário)
		return true
	}
	
	// Apenas admin e filial podem atribuir ordens
	return false
}
