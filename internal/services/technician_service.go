package services

import (
	"time"

	"tradicao/internal/models"
	"tradicao/internal/repository"
)

// TechnicianRepository define a interface para o repositório do técnico
type TechnicianRepository interface {
	CreateSpecialty(specialty *models.TechnicianSpecialty) error
	ListSpecialties() ([]models.TechnicianSpecialty, error)
	UpdateSpecialty(specialty *models.TechnicianSpecialty) error
	DeleteSpecialty(id uint) error
	GetSpecialty(id uint) (*models.TechnicianSpecialty, error)

	CreateBranchAssociation(association *models.TechnicianBranch) error
	UpdateBranchAssociation(association *models.TechnicianBranch) error
	DeleteBranchAssociation(technicianID, branchID uint) error
	GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error)
	ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error)

	CreateMaintenanceHistory(history *models.MaintenanceHistory) error
	ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error)
	GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error)
}

// TechnicianServiceInterface defines the interface for technician services
// This interface is implemented by TechnicianService and adapted by TechnicianServiceAdapter
type TechnicianServiceInterface interface {
	// Métodos básicos
	CreateSpecialty(specialty *models.TechnicianSpecialty) error
	ListSpecialties() ([]models.TechnicianSpecialty, error)
	UpdateSpecialty(specialty *models.TechnicianSpecialty) error
	DeleteSpecialty(id uint) error
	GetSpecialty(id uint) (*models.TechnicianSpecialty, error)

	// Métodos de associação
	CreateBranchAssociation(association *models.TechnicianBranch) error
	UpdateBranchAssociation(association *models.TechnicianBranch) error
	DeleteBranchAssociation(technicianID, branchID uint) error
	GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error)
	ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error)

	// Métodos de histórico
	CreateMaintenanceHistory(history *models.MaintenanceHistory) error
	ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error)
	GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error)

	// Métodos estendidos
	GetSpecialtyByID(id uint) (*models.TechnicianSpecialty, error)
	CreateTechnicianBranch(branch *models.TechnicianBranch) error
	GetTechnicianBranches(technicianID uint) ([]models.TechnicianBranch, error)
	GetBranchTechnicians(branchID uint) ([]models.Technician, error)
	DeleteTechnicianBranch(technicianID, branchID, specialtyID uint) error
	GetTechnicianHistory(technicianID uint) ([]models.MaintenanceHistory, error)
	GetEquipmentHistory(equipmentID uint) ([]models.MaintenanceHistory, error)
	GetOrderHistory(orderID uint) ([]models.MaintenanceHistory, error)
	GetBranchTechniciansFiltered(branchID uint, equipmentTypeID *uint) ([]models.Technician, error)
	GetBranchInternalTechniciansFiltered(branchID uint, equipmentTypeID *uint) ([]models.Technician, error)
	GetTechnicianByUserID(userID uint) (*models.User, error)
}

type TechnicianService struct {
	repo repository.TechnicianRepository
}

func NewTechnicianService(repo repository.TechnicianRepository) *TechnicianService {
	return &TechnicianService{repo: repo}
}

// CreateSpecialty cria uma nova especialidade
func (s *TechnicianService) CreateSpecialty(specialty *models.TechnicianSpecialty) error {
	return s.repo.CreateSpecialty(specialty)
}

// ListSpecialties lista todas as especialidades
func (s *TechnicianService) ListSpecialties() ([]models.TechnicianSpecialty, error) {
	return s.repo.ListSpecialties()
}

// UpdateSpecialty atualiza uma especialidade existente
func (s *TechnicianService) UpdateSpecialty(specialty *models.TechnicianSpecialty) error {
	return s.repo.UpdateSpecialty(specialty)
}

// DeleteSpecialty remove uma especialidade
func (s *TechnicianService) DeleteSpecialty(id uint) error {
	return s.repo.DeleteSpecialty(id)
}

// GetSpecialty obtém uma especialidade pelo ID
func (s *TechnicianService) GetSpecialty(id uint) (*models.TechnicianSpecialty, error) {
	return s.repo.GetSpecialty(id)
}

// CreateBranchAssociation cria uma nova associação entre técnico e filial
func (s *TechnicianService) CreateBranchAssociation(association *models.TechnicianBranch) error {
	return s.repo.CreateBranchAssociation(association)
}

// UpdateBranchAssociation atualiza uma associação existente
func (s *TechnicianService) UpdateBranchAssociation(association *models.TechnicianBranch) error {
	return s.repo.UpdateBranchAssociation(association)
}

// DeleteBranchAssociation remove uma associação
func (s *TechnicianService) DeleteBranchAssociation(technicianID, branchID uint) error {
	return s.repo.DeleteBranchAssociation(technicianID, branchID)
}

// GetBranchAssociation obtém uma associação pelo ID do técnico e da filial
func (s *TechnicianService) GetBranchAssociation(technicianID, branchID uint) (*models.TechnicianBranch, error) {
	return s.repo.GetBranchAssociation(technicianID, branchID)
}

// ListBranchAssociations lista todas as associações de um técnico
func (s *TechnicianService) ListBranchAssociations(technicianID uint) ([]models.TechnicianBranch, error) {
	return s.repo.ListBranchAssociations(technicianID)
}

// CreateMaintenanceHistory cria um novo registro no histórico de manutenção
func (s *TechnicianService) CreateMaintenanceHistory(history *models.MaintenanceHistory) error {
	return s.repo.CreateMaintenanceHistory(history)
}

// ListMaintenanceHistory lista o histórico de manutenção de um técnico
func (s *TechnicianService) ListMaintenanceHistory(technicianID uint, startDate, endDate time.Time) ([]models.MaintenanceHistory, error) {
	return s.repo.ListMaintenanceHistory(technicianID, startDate, endDate)
}

// GetMaintenanceHistory obtém um registro do histórico pelo ID
func (s *TechnicianService) GetMaintenanceHistory(id uint) (*models.MaintenanceHistory, error) {
	return s.repo.GetMaintenanceHistory(id)
}

// GetBranchTechniciansFiltered retorna técnicos ativos vinculados à filial e ao tipo de equipamento
func (s *TechnicianService) GetBranchTechniciansFiltered(branchID uint, equipmentTypeID *uint) ([]models.Technician, error) {
	if equipmentTypeID == nil {
		return s.repo.GetBranchTechnicians(branchID)
	}
	return s.repo.GetBranchTechniciansByEquipment(branchID, *equipmentTypeID)
}

// GetBranchInternalTechniciansFiltered retorna técnicos da filial sem vínculo com prestadora e filtrados por tipo de equipamento
func (s *TechnicianService) GetBranchInternalTechniciansFiltered(branchID uint, equipmentTypeID *uint) ([]models.Technician, error) {
	if equipmentTypeID == nil {
		return s.repo.GetBranchInternalTechnicians(branchID)
	}
	return s.repo.GetBranchInternalTechniciansByEquipment(branchID, *equipmentTypeID)
}
