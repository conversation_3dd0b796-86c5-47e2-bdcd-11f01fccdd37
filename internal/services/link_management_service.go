package services

import (
	"errors"
	"fmt"
	"time"
	"tradicao/internal/models"
	"tradicao/internal/repository"

	"gorm.io/gorm"
)

// LinkManagementService é o serviço para gerenciar vínculos entre filiais, prestadoras e técnicos
type LinkManagementService struct {
	db                  *gorm.DB
	branchRepo          repository.IBranchRepository
	technicianRepo      repository.TechnicianRepository
	serviceProviderRepo repository.ServiceProviderRepository
}

// NewLinkManagementService cria uma nova instância do serviço
func NewLinkManagementService(
	db *gorm.DB,
	branchRepo repository.IBranchRepository,
	technicianRepo repository.TechnicianRepository,
	serviceProviderRepo repository.ServiceProviderRepository,
) *LinkManagementService {
	return &LinkManagementService{
		db:                  db,
		branchRepo:          branchRepo,
		technicianRepo:      technicianRepo,
		serviceProviderRepo: serviceProviderRepo,
	}
}

// LinkProviderToBranch vincula um prestador a uma filial
func (s *LinkManagementService) LinkProviderToBranch(providerID, branchID uint) error {
	// Verificar se o prestador existe
	provider, err := s.serviceProviderRepo.FindByID(providerID)
	if err != nil {
		return fmt.Errorf("erro ao buscar prestador: %w", err)
	}
	if provider.ID == 0 {
		return errors.New("prestador não encontrado")
	}

	// Verificar se a filial existe
	exists, err := s.branchRepo.BranchExists(branchID)
	if err != nil {
		return fmt.Errorf("erro ao verificar filial: %w", err)
	}
	if !exists {
		return errors.New("filial não encontrada")
	}

	// Verificar se o vínculo já existe
	providerIDs, err := s.branchRepo.GetLinkedProviders(branchID)
	if err != nil {
		return fmt.Errorf("erro ao verificar vínculos existentes: %w", err)
	}
	for _, id := range providerIDs {
		if id == providerID {
			return errors.New("prestador já está vinculado a esta filial")
		}
	}

	// Criar o vínculo
	return s.branchRepo.LinkProvider(branchID, providerID)
}

// UpdateProviderBranchLink atualiza um vínculo entre prestador e filial
func (s *LinkManagementService) UpdateProviderBranchLink(id, providerID, branchID uint, status string) error {
	// Verificar se o vínculo existe
	var count int64
	if err := s.db.Table("branch_providers").
		Where("id = ?", id).
		Count(&count).Error; err != nil {
		return fmt.Errorf("erro ao verificar vínculo existente: %w", err)
	}

	if count == 0 {
		return errors.New("vínculo não encontrado")
	}

	// Verificar se o prestador existe
	provider, err := s.serviceProviderRepo.FindByID(providerID)
	if err != nil {
		return fmt.Errorf("erro ao buscar prestador: %w", err)
	}
	if provider.ID == 0 {
		return errors.New("prestador não encontrado")
	}

	// Verificar se a filial existe
	exists, err := s.branchRepo.BranchExists(branchID)
	if err != nil {
		return fmt.Errorf("erro ao verificar filial: %w", err)
	}
	if !exists {
		return errors.New("filial não encontrada")
	}

	// Atualizar o vínculo
	if err := s.db.Exec(
		"UPDATE branch_providers SET service_provider_id = ?, branch_id = ?, status = ?, updated_at = ? WHERE id = ?",
		providerID, branchID, status, time.Now(), id,
	).Error; err != nil {
		return fmt.Errorf("erro ao atualizar vínculo: %w", err)
	}

	return nil
}

// UnlinkProviderFromBranch remove o vínculo entre um prestador e uma filial
func (s *LinkManagementService) UnlinkProviderFromBranch(providerID, branchID uint) error {
	// Verificar se o vínculo existe
	providerIDs, err := s.branchRepo.GetLinkedProviders(branchID)
	if err != nil {
		return fmt.Errorf("erro ao verificar vínculos existentes: %w", err)
	}

	found := false
	for _, id := range providerIDs {
		if id == providerID {
			found = true
			break
		}
	}

	if !found {
		return errors.New("prestador não está vinculado a esta filial")
	}

	// Remover o vínculo
	return s.branchRepo.UnlinkProvider(branchID, providerID)
}

// GetBranchProviders retorna os prestadores vinculados a uma filial
func (s *LinkManagementService) GetBranchProviders(branchID uint) ([]models.ServiceProvider, error) {
	// Verificar se a filial existe
	exists, err := s.branchRepo.BranchExists(branchID)
	if err != nil {
		return nil, fmt.Errorf("erro ao verificar filial: %w", err)
	}
	if !exists {
		return nil, errors.New("filial não encontrada")
	}

	// Obter IDs dos prestadores vinculados
	providerIDs, err := s.branchRepo.GetLinkedProviders(branchID)
	if err != nil {
		return nil, fmt.Errorf("erro ao obter prestadores vinculados: %w", err)
	}

	// Se não houver prestadores vinculados, retornar lista vazia
	if len(providerIDs) == 0 {
		return []models.ServiceProvider{}, nil
	}

	// Obter detalhes dos prestadores
	providers := make([]models.ServiceProvider, 0, len(providerIDs))
	for _, id := range providerIDs {
		provider, err := s.serviceProviderRepo.FindByID(id)
		if err != nil {
			return nil, fmt.Errorf("erro ao buscar prestador %d: %w", id, err)
		}
		providers = append(providers, provider)
	}

	return providers, nil
}

// LinkTechnicianToBranch vincula um técnico a uma filial
func (s *LinkManagementService) LinkTechnicianToBranch(technicianID, branchID, specialtyID uint) error {
	association := &models.TechnicianBranch{
		TechnicianID: int(technicianID),
		BranchID:     int(branchID),
	}
	return s.technicianRepo.CreateBranchAssociation(association)
}

// UnlinkTechnicianFromBranch remove o vínculo entre um técnico e uma filial
func (s *LinkManagementService) UnlinkTechnicianFromBranch(technicianID, branchID uint) error {
	return s.technicianRepo.DeleteBranchAssociation(uint(technicianID), uint(branchID))
}

// GetBranchTechnicians retorna os técnicos vinculados a uma filial
func (s *LinkManagementService) GetBranchTechnicians(branchID uint) ([]models.Technician, error) {
	return s.technicianRepo.GetBranchTechnicians(branchID)
}

// GetProviderTechnicians retorna os técnicos vinculados a um prestador
func (s *LinkManagementService) GetProviderTechnicians(providerID uint) ([]models.User, error) {
	return s.serviceProviderRepo.GetTechnicians(providerID)
}

// GetTechnicianBranches retorna as filiais vinculadas a um técnico
func (s *LinkManagementService) GetTechnicianBranches(technicianID uint) ([]models.TechnicianBranch, error) {
	return s.technicianRepo.ListBranchAssociations(technicianID)
}

// GetProviderBranches retorna as filiais vinculadas a um prestador
func (s *LinkManagementService) GetProviderBranches(providerID uint) ([]models.Branch, error) {
	// Implementar conforme necessário
	return nil, nil
}

// GetAllProviderBranchLinks retorna todos os vínculos entre prestadores e filiais
func (s *LinkManagementService) GetAllProviderBranchLinks() ([]map[string]interface{}, error) {
	var links []struct {
		ServiceProviderID uint      `json:"service_provider_id"`
		BranchID          uint      `json:"branch_id"`
		CreatedAt         time.Time `json:"created_at"`
		UpdatedAt         time.Time `json:"updated_at"`
	}

	// Consultar os vínculos na tabela branch_providers
	if err := s.db.Table("branch_providers").Find(&links).Error; err != nil {
		return nil, fmt.Errorf("erro ao consultar vínculos: %w", err)
	}

	// Converter para o formato esperado e adicionar informações adicionais
	result := make([]map[string]interface{}, len(links))
	for i, link := range links {
		// Buscar informações adicionais da prestadora
		var provider models.ServiceProvider
		if err := s.db.First(&provider, link.ServiceProviderID).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("erro ao buscar prestadora %d: %w", link.ServiceProviderID, err)
			}
			provider.Name = "Desconhecida"
		}

		// Buscar informações adicionais da filial
		var branch models.Branch
		if err := s.db.First(&branch, link.BranchID).Error; err != nil {
			if !errors.Is(err, gorm.ErrRecordNotFound) {
				return nil, fmt.Errorf("erro ao buscar filial %d: %w", link.BranchID, err)
			}
			branch.Name = "Desconhecida"
		}

		result[i] = map[string]interface{}{
			"service_provider_id": link.ServiceProviderID,
			"branch_id":           link.BranchID,
			"created_at":          link.CreatedAt,
			"provider_name":       provider.Name,
			"branch_name":         branch.Name,
		}
	}

	return result, nil
}

// GetAllTechnicianBranchLinks retorna todos os vínculos entre técnicos e filiais
func (s *LinkManagementService) GetAllTechnicianBranchLinks() ([]models.TechnicianBranch, error) {
	var links []models.TechnicianBranch

	// Consultar os vínculos no banco de dados
	if err := s.db.Find(&links).Error; err != nil {
		return nil, fmt.Errorf("erro ao consultar vínculos: %w", err)
	}

	return links, nil
}

// GetAllProviderTechnicianLinks retorna todos os vínculos entre prestadoras e técnicos
func (s *LinkManagementService) GetAllProviderTechnicianLinks() ([]map[string]interface{}, error) {
	var links []struct {
		ProviderID   uint      `json:"provider_id"`
		TechnicianID uint      `json:"technician_id"`
		CreatedAt    time.Time `json:"created_at"`
	}

	// Consultar os vínculos no banco de dados
	if err := s.db.Table("provider_technicians").Find(&links).Error; err != nil {
		return nil, fmt.Errorf("erro ao consultar vínculos: %w", err)
	}

	// Buscar todos os providers de uma vez
	var allProviders []models.ServiceProvider
	s.db.Find(&allProviders)
	providerMap := make(map[uint]string)
	for _, p := range allProviders {
		providerMap[p.ID] = p.Name
	}

	// Buscar todos os técnicos de uma vez
	var allTechnicians []models.User
	s.db.Find(&allTechnicians)
	technicianMap := make(map[uint]models.User)
	for _, t := range allTechnicians {
		technicianMap[t.ID] = t
	}

	// Converter para o formato esperado
	result := make([]map[string]interface{}, len(links))
	for i, link := range links {
		providerName := providerMap[link.ProviderID]
		if providerName == "" {
			providerName = "Desconhecida"
		}
		technician := technicianMap[link.TechnicianID]
		result[i] = map[string]interface{}{
			"provider_id":      link.ProviderID,
			"technician_id":    link.TechnicianID,
			"created_at":       link.CreatedAt,
			"provider_name":    providerName,
			"technician_name":  technician.Name,
			"technician_email": technician.Email,
		}
	}

	return result, nil
}

// LinkProviderToTechnician vincula uma prestadora a um técnico
func (s *LinkManagementService) LinkProviderToTechnician(providerID, technicianID uint) error {
	// Verificar se a prestadora existe
	provider, err := s.serviceProviderRepo.FindByID(providerID)
	if err != nil {
		return fmt.Errorf("erro ao buscar prestadora: %w", err)
	}
	if provider.ID == 0 {
		return errors.New("prestadora não encontrada")
	}

	// Verificar se o técnico existe
	var technician models.User
	if err := s.db.First(&technician, technicianID).Error; err != nil {
		return fmt.Errorf("erro ao buscar técnico: %w", err)
	}

	// Verificar se o vínculo já existe
	var count int64
	if err := s.db.Table("provider_technicians").
		Where("provider_id = ? AND technician_id = ?", providerID, technicianID).
		Count(&count).Error; err != nil {
		return fmt.Errorf("erro ao verificar vínculo existente: %w", err)
	}

	if count > 0 {
		return errors.New("técnico já está vinculado a esta prestadora")
	}

	// Criar o vínculo
	if err := s.db.Exec(
		"INSERT INTO provider_technicians (provider_id, technician_id, created_at) VALUES (?, ?, ?)",
		providerID, technicianID, time.Now(),
	).Error; err != nil {
		return fmt.Errorf("erro ao criar vínculo: %w", err)
	}

	return nil
}

// UpdateProviderTechnicianLink atualiza um vínculo entre prestadora e técnico
func (s *LinkManagementService) UpdateProviderTechnicianLink(id, providerID, technicianID uint, status string) error {
	// Verificar se o vínculo existe
	var count int64
	if err := s.db.Table("provider_technicians").
		Where("id = ?", id).
		Count(&count).Error; err != nil {
		return fmt.Errorf("erro ao verificar vínculo existente: %w", err)
	}

	if count == 0 {
		return errors.New("vínculo não encontrado")
	}

	// Verificar se a prestadora existe
	provider, err := s.serviceProviderRepo.FindByID(providerID)
	if err != nil {
		return fmt.Errorf("erro ao buscar prestadora: %w", err)
	}
	if provider.ID == 0 {
		return errors.New("prestadora não encontrada")
	}

	// Verificar se o técnico existe
	var technician models.User
	if err := s.db.First(&technician, technicianID).Error; err != nil {
		return fmt.Errorf("erro ao buscar técnico: %w", err)
	}

	// Atualizar o vínculo
	if err := s.db.Exec(
		"UPDATE provider_technicians SET provider_id = ?, technician_id = ?, status = ?, updated_at = ? WHERE id = ?",
		providerID, technicianID, status, time.Now(), id,
	).Error; err != nil {
		return fmt.Errorf("erro ao atualizar vínculo: %w", err)
	}

	return nil
}

// UnlinkProviderFromTechnician remove o vínculo entre uma prestadora e um técnico
func (s *LinkManagementService) UnlinkProviderFromTechnician(providerID, technicianID uint) error {
	// Verificar se o vínculo existe
	var count int64
	if err := s.db.Table("provider_technicians").
		Where("provider_id = ? AND technician_id = ?", providerID, technicianID).
		Count(&count).Error; err != nil {
		return fmt.Errorf("erro ao verificar vínculo existente: %w", err)
	}

	if count == 0 {
		return errors.New("técnico não está vinculado a esta prestadora")
	}

	// Remover o vínculo
	if err := s.db.Exec(
		"DELETE FROM provider_technicians WHERE provider_id = ? AND technician_id = ?",
		providerID, technicianID,
	).Error; err != nil {
		return fmt.Errorf("erro ao remover vínculo: %w", err)
	}

	return nil
}

// InheritBranchLinksFromProvider herda os vínculos de filiais de uma prestadora para um técnico
func (s *LinkManagementService) InheritBranchLinksFromProvider(providerID, technicianID uint) (int, error) {
	// Verificar se a prestadora existe
	provider, err := s.serviceProviderRepo.FindByID(providerID)
	if err != nil {
		return 0, fmt.Errorf("erro ao buscar prestadora: %w", err)
	}
	if provider.ID == 0 {
		return 0, errors.New("prestadora não encontrada")
	}

	// Verificar se o técnico existe
	var technician models.User
	if err := s.db.First(&technician, technicianID).Error; err != nil {
		return 0, fmt.Errorf("erro ao buscar técnico: %w", err)
	}

	// Verificar se o técnico está vinculado à prestadora
	var count int64
	if err := s.db.Table("provider_technicians").
		Where("provider_id = ? AND technician_id = ?", providerID, technicianID).
		Count(&count).Error; err != nil {
		return 0, fmt.Errorf("erro ao verificar vínculo existente: %w", err)
	}

	if count == 0 {
		return 0, errors.New("técnico não está vinculado a esta prestadora")
	}

	// Obter filiais vinculadas à prestadora
	var branchLinks []struct {
		BranchID uint `json:"branch_id" gorm:"column:branch_id"`
	}
	if err := s.db.Table("branch_providers").
		Where("service_provider_id = ?", providerID).
		Find(&branchLinks).Error; err != nil {
		return 0, fmt.Errorf("erro ao buscar filiais vinculadas à prestadora: %w", err)
	}

	if len(branchLinks) == 0 {
		return 0, nil // Nenhuma filial para herdar
	}

	// Obter especialidade padrão (ou criar uma se não existir)
	var defaultSpecialty models.TechnicianSpecialty
	if err := s.db.Where("name = ?", "Geral").First(&defaultSpecialty).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			// Criar especialidade padrão
			// Criar especialidade padrão com array vazio
			if err := s.db.Exec("INSERT INTO technician_specialties (name, description, equipment_types) VALUES (?, ?, ARRAY[]::text[])",
				"Geral", "Especialidade padrão para técnicos").Error; err != nil {
				return 0, fmt.Errorf("erro ao criar especialidade padrão: %w", err)
			}

			// Buscar a especialidade recém-criada
			if err := s.db.Where("name = ?", "Geral").First(&defaultSpecialty).Error; err != nil {
				return 0, fmt.Errorf("erro ao buscar especialidade padrão após criação: %w", err)
			}
		} else {
			return 0, fmt.Errorf("erro ao buscar especialidade padrão: %w", err)
		}
	}

	// Vincular o técnico a cada filial
	createdLinks := 0
	for _, link := range branchLinks {
		var existingLink int64
		if err := s.db.Model(&models.TechnicianBranch{}).
			Where("technician_id = ? AND branch_id = ?", int(technicianID), int(link.BranchID)).
			Count(&existingLink).Error; err != nil {
			return createdLinks, fmt.Errorf("erro ao verificar vínculo existente: %w", err)
		}
		if existingLink > 0 {
			continue
		}
		techBranch := models.TechnicianBranch{
			TechnicianID: int(technicianID),
			BranchID:     int(link.BranchID),
		}
		if err := s.db.Create(&techBranch).Error; err != nil {
			return createdLinks, fmt.Errorf("erro ao criar vínculo com filial %d: %w", link.BranchID, err)
		}
		createdLinks++
	}

	return createdLinks, nil
}

// GetAllTechnicianEquipmentLinks retorna todos os vínculos entre técnicos e tipos de equipamento
func (s *LinkManagementService) GetAllTechnicianEquipmentLinks() ([]models.TechnicianEquipmentType, error) {
	var links []models.TechnicianEquipmentType

	if err := s.db.Preload("Technician").Preload("EquipmentType").Find(&links).Error; err != nil {
		return nil, fmt.Errorf("erro ao consultar vínculos técnico-equipamento: %w", err)
	}

	return links, nil
}

// LinkTechnicianToEquipment vincula um técnico a um tipo de equipamento
func (s *LinkManagementService) LinkTechnicianToEquipment(technicianID, equipmentTypeID uint, description, verificationDate string) error {
	// Verificar se o técnico existe
	var technician models.User
	if err := s.db.Where("id = ? AND role IN (?, ?)", technicianID, "technician", "tecnico").First(&technician).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("técnico não encontrado")
		}
		return fmt.Errorf("erro ao buscar técnico: %w", err)
	}

	// Verificar se o tipo de equipamento existe
	var equipmentType models.EquipmentType
	if err := s.db.First(&equipmentType, equipmentTypeID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("tipo de equipamento não encontrado")
		}
		return fmt.Errorf("erro ao buscar tipo de equipamento: %w", err)
	}

	// Verificar se o vínculo já existe
	var existingLink models.TechnicianEquipmentType
	if err := s.db.Where("technician_id = ? AND equipment_type_id = ?", technicianID, equipmentTypeID).First(&existingLink).Error; err == nil {
		return errors.New("técnico já está vinculado a este tipo de equipamento")
	}

	// Criar o vínculo
	link := models.TechnicianEquipmentType{
		TechnicianID:    technicianID,
		EquipmentTypeID: equipmentTypeID,
		IsActive:        true,
	}

	if err := s.db.Create(&link).Error; err != nil {
		return fmt.Errorf("erro ao criar vínculo: %w", err)
	}

	return nil
}

// UpdateTechnicianEquipmentLink atualiza um vínculo entre técnico e equipamento
func (s *LinkManagementService) UpdateTechnicianEquipmentLink(id, technicianID, equipmentTypeID uint, description, verificationDate string) error {
	// Verificar se o vínculo existe
	var link models.TechnicianEquipmentType
	if err := s.db.First(&link, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("vínculo não encontrado")
		}
		return fmt.Errorf("erro ao buscar vínculo: %w", err)
	}

	// Verificar se o técnico existe
	var technician models.User
	if err := s.db.Where("id = ? AND role IN (?, ?)", technicianID, "technician", "tecnico").First(&technician).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("técnico não encontrado")
		}
		return fmt.Errorf("erro ao buscar técnico: %w", err)
	}

	// Verificar se o tipo de equipamento existe
	var equipmentType models.EquipmentType
	if err := s.db.First(&equipmentType, equipmentTypeID).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("tipo de equipamento não encontrado")
		}
		return fmt.Errorf("erro ao buscar tipo de equipamento: %w", err)
	}

	// Atualizar o vínculo
	updates := map[string]interface{}{
		"technician_id":     technicianID,
		"equipment_type_id": equipmentTypeID,
	}

	if err := s.db.Model(&link).Updates(updates).Error; err != nil {
		return fmt.Errorf("erro ao atualizar vínculo: %w", err)
	}

	return nil
}

// UnlinkTechnicianFromEquipment remove o vínculo entre um técnico e um tipo de equipamento
func (s *LinkManagementService) UnlinkTechnicianFromEquipment(id uint) error {
	// Verificar se o vínculo existe
	var link models.TechnicianEquipmentType
	if err := s.db.First(&link, id).Error; err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			return errors.New("vínculo não encontrado")
		}
		return fmt.Errorf("erro ao buscar vínculo: %w", err)
	}

	// Remover o vínculo
	if err := s.db.Delete(&link).Error; err != nil {
		return fmt.Errorf("erro ao remover vínculo: %w", err)
	}

	return nil
}
