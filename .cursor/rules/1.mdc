---
alwaysApply: true
---
## 🔍 **METODOLOGIA OBRIGATÓRIA**

### **1. ANÁLISE COMPLETA E ABRANGENTE**
- **ANTES** de qualquer afirmação ou ação, fazer busca completa em TODO o projeto
- **NUNCA** afirmar que algo "não existe" sem verificar 100% do código
- **SEMPRE** usar múltiplas buscas com diferentes padrões para garantir cobertura total

### **2. CONFIRMAÇÃO EXPLÍCITA**
- **NUNCA** executar modificações sem solicitação explícita do usuário
- **SEMPRE** confirmar cada ação antes de executar
- **SEMPRE** explicar exatamente o que será feito e por quê

### **3. ANÁLISE RACIONAL E ESTRATÉGICA**
- **ANTES** de cada ação, fazer análise racional: "Estou desviando do propósito?"
- **SEMPRE** verificar se a ação resolve o problema real
- **NUNCA** fazer suposições ou "achismos"

### **4. ANÁLISE E RESPOSTAS**
- Toda análise de erro deve ser feita consultando 100% do código relevante dos arquivos envolvidos.
- Jamais responder com suposições, achismos ou resultados sem certeza.
- Só apresentar respostas e soluções com total certeza, baseadas na leitura e compreensão do código do projeto.

### **5. ALTERAÇÕES DE CÓDIGO**
- Antes de aplicar, editar ou alterar qualquer arquivo, a IA deve executar uma análise panorâmica para detectar todos os setores do projeto que utilizam os dados ou funções que serão alterados.
- Jamais alterar dados, modelos ou lógicas que possam ocasionar erros em outros pontos do projeto.
- Garantir que toda alteração seja segura, compatível e não quebre funcionalidades existentes.

---

## 🛠️ **EXECUÇÃO DE SCRIPTS**

### **COMANDOS OBRIGATÓRIOS**
- **PARA EXECUTAR SCRIPTS NO TERMINAL**: Usar `@run_terminal_cmd ./nome_do_script.sh`
- **PARA ABRIR ARQUIVOS NO EDITOR**: Usar `@nome_do_arquivo`

### **EXEMPLOS**
- `@run_terminal_cmd ./iniciar_rapido.sh` → Executa o script no terminal
- `@iniciar_rapido.sh` → Abre o arquivo no editor
- `@run_terminal_cmd bash iniciar_rapido.sh` → Executa com bash explicitamente

### **REGRAS**
- **SEMPRE** usar `@run_terminal_cmd` para executar scripts, não apenas `@nome_do_script`

---

## 🚨 **REGRA ABSOLUTA**

- **PROIBIDO** hipóteses, achismos e suposições.
- **SÓ** responder com fatos comprovados por código, banco ou documentação oficial do projeto.

---

**Essas regras devem ser seguidas por qualquer agente/IA que atue neste projeto.** 
- Só responder com fatos comprovados por código, banco ou documentação oficial do projeto.

---

**Essas regras devem ser seguidas por qualquer agente/IA que atue neste projeto.** 