package main

import (
	"context"
	"fmt"
	"html/template"
	"log"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"strings"
	"syscall"
	"time"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"

	"tradicao/internal/config"
	"tradicao/internal/controllers"
	"tradicao/internal/database"
	"tradicao/internal/ent"
	"tradicao/internal/handlers"
	"tradicao/internal/middleware"
	"tradicao/internal/models"
	"tradicao/internal/permissions"
	"tradicao/internal/repository"
	"tradicao/internal/routes"
	"tradicao/internal/services"
	"tradicao/internal/setup"
	webRoutes "tradicao/web/routes"
	"tradicao/web/templates"

	"github.com/prometheus/client_golang/prometheus/promhttp"
)

// Função auxiliar para formatar datas nullable
func formatNullableTime(t *time.Time) string {
	if t == nil {
		return ""
	}
	return t.Format("2006-01-02")
}

func main() {
	// Usar o diretório atual como diretório de trabalho
	workDir, err := os.Getwd()
	if err != nil {
		log.Printf("ERRO CRÍTICO: Erro ao obter diretório de trabalho atual: %v", err)
		os.Exit(1)
	}
	log.Printf("Usando diretório de trabalho: %s", workDir)

	// Carrega configurações
	cfg, err := config.LoadConfig()
	if err != nil {
		log.Printf("ERRO CRÍTICO: Erro ao carregar configurações: %v", err)
		os.Exit(1)
	}

	// Configurar o roteador Gin
	router := gin.New()

	// Configurar o modo de release para produção
	if os.Getenv("GIN_MODE") == "release" {
		gin.SetMode(gin.ReleaseMode)
	} else {
		gin.SetMode(gin.DebugMode)
	}

	// A conexão com o banco de dados será inicializada pelo GORM mais abaixo

	// A inicialização dos serviços e middlewares que dependem do DB foi movida para depois da inicialização do GORM

	// Configurar proxy confiável
	router.SetTrustedProxies([]string{"127.0.0.1"})

	// Configurar arquivos estáticos
	router.Static("/static", "./web/static")

	// Unificar todas as funções de template em um único FuncMap
	funcMap := template.FuncMap{
		"formatDateTime": func(t time.Time) string {
			return t.Format("02/01/2006 15:04")
		},
		"formatDate": func(t time.Time) string {
			return t.Format("02/01/2006")
		},
		"formatCurrency": func(value float64) string {
			return fmt.Sprintf("R$ %.2f", value)
		},
		"add": func(a, b int) int {
			return a + b
		},
		"sub": func(a, b int) int {
			return a - b
		},
		"mul": func(a, b int) int {
			return a * b
		},
		"upper": strings.ToUpper,
		"div": func(a, b int) int {
			if b == 0 {
				return 0
			}
			return a / b
		},
		"defaultString": func(s, defaultValue string) string {
			if s == "" {
				return defaultValue
			}
			return s
		},
		"statusToClass": func(status string) string {
			switch status {
			case "pending":
				return "warning"
			case "in_progress":
				return "info"
			case "completed":
				return "success"
			case "cancelled":
				return "danger"
			default:
				return "secondary"
			}
		},
		"formatStatus": func(status string) string {
			switch status {
			case "pending":
				return "Pendente"
			case "in_progress":
				return "Em Andamento"
			case "completed":
				return "Concluído"
			case "cancelled":
				return "Cancelado"
			default:
				return status
			}
		},
		"formatPriority": func(priority string) string {
			switch priority {
			case "low":
				return "Baixa"
			case "medium":
				return "Média"
			case "high":
				return "Alta"
			case "critical":
				return "Crítica"
			default:
				return priority
			}
		},
		"default": func(value, defaultValue interface{}) interface{} {
			if value == nil {
				return defaultValue
			}

			// Verificar se é string vazia
			if str, ok := value.(string); ok && str == "" {
				return defaultValue
			}

			// Verificar se é zero numérico
			switch v := value.(type) {
			case int:
				if v == 0 {
					return defaultValue
				}
			case float64:
				if v == 0 {
					return defaultValue
				}
			}

			return value
		},
		"statusToIcon": func(status string) string {
			switch status {
			case "pending":
				return "bi-hourglass-split"
			case "in_progress":
				return "bi-gear-wide-connected"
			case "completed":
				return "bi-check-circle"
			case "cancelled":
				return "bi-x-circle"
			default:
				return "bi-question-circle"
			}
		},
		"priorityToIcon": func(priority string) string {
			switch priority {
			case "low":
				return "bi-arrow-down-circle"
			case "medium":
				return "bi-dash-circle"
			case "high":
				return "bi-arrow-up-circle"
			case "critical":
				return "bi-exclamation-triangle"
			default:
				return "bi-question-circle"
			}
		},
		"formatTime": func(t time.Time) string {
			return t.Format("15:04")
		},
		"float64": func(v interface{}) float64 {
			switch val := v.(type) {
			case float64:
				return val
			case float32:
				return float64(val)
			case int:
				return float64(val)
			case int64:
				return float64(val)
			case int32:
				return float64(val)
			case string:
				f, err := strconv.ParseFloat(val, 64)
				if err != nil {
					return 0
				}
				return f
			default:
				return 0
			}
		},
		"hasSuffix": strings.HasSuffix,
	}

	// Adicionar funções do pacote templates.TemplateFuncs()
	for name, fn := range templates.TemplateFuncs() {
		funcMap[name] = fn
	}

	// Criar template com as funções unificadas
	tmpl := template.New("").Funcs(funcMap)

	// Carregar templates de forma mais específica para garantir que todos sejam incluídos
	log.Println("Carregando templates...")

	// Carregar templates das páginas principais
	tmpl = template.Must(tmpl.ParseGlob("web/templates/**/*.html"))

	// Carregar especificamente templates de layouts para garantir que sidebar seja incluído
	tmpl = template.Must(tmpl.ParseGlob("web/templates/layouts/*.html"))

	// Verificar se o template sidebar foi carregado corretamente
	sidebarTemplate := tmpl.Lookup("layouts/sidebar.html")
	if sidebarTemplate != nil {
		log.Println("✓ Template 'layouts/sidebar.html' carregado com sucesso")
	} else {
		log.Println("⚠ Template 'layouts/sidebar.html' não encontrado")
	}

	// Verificar se o template sidebar alias foi carregado
	sidebarAliasTemplate := tmpl.Lookup("sidebar")
	if sidebarAliasTemplate != nil {
		log.Println("✓ Template 'sidebar' (alias) carregado com sucesso")
	} else {
		log.Println("⚠ Template 'sidebar' (alias) não encontrado")
	}

	// Listar todos os templates carregados para debug
	log.Printf("Templates carregados: %d", len(tmpl.Templates()))
	for _, t := range tmpl.Templates() {
		if t.Name() != "" {
			log.Printf("  - %s", t.Name())
		}
	}

	router.SetHTMLTemplate(tmpl)

	log.Println("Templates carregados e configurados com sucesso")

	// Inicializa o GORM
	db, err := database.Connect()
	if err != nil {
		log.Printf("ERRO CRÍTICO: Erro ao conectar ao banco de dados via GORM: %v", err)
		os.Exit(1)
	}

	// Removido bloco de AutoMigrate para evitar conflitos e erros de migração automática.

	// Inicializar serviços necessários para o middleware
	userRepo := repository.NewGormUserRepository()
	authService := services.NewAuthService(userRepo, cfg)

	// Configurar middlewares que dependem do DB
	router.Use(middleware.SessionAuthMiddleware(authService.(*services.AuthService), db))

	// Adicionar o banco de dados ao middleware para estar disponível no contexto
	router.Use(func(c *gin.Context) {
		c.Set("db", db)
		c.Next()
	})

	// INICIALIZAR SISTEMA UNIFICADO DE PERMISSÕES
	log.Println("Inicializando sistema unificado de permissões...")
	if err := permissions.InitializeGlobalPermissions(db, "data/permissions.yaml"); err != nil {
		log.Printf("AVISO: Erro ao inicializar sistema de permissões: %v", err)
		log.Println("Continuando sem sistema de permissões...")
	}

	// Configurar middlewares centralizados
	centralizedConfig := middleware.NewCentralizedMiddlewareConfig(db)
	centralizedConfig.ApplyGlobalMiddlewares(router)

	// Painel de vínculos removido - usando apenas novo_link_management

	// Inicializa cliente ENT usando o pool de conexões existente
	err = ent.InitClientWithPool(gin.Mode() != gin.ReleaseMode)
	if err != nil {
		log.Printf("ERRO CRÍTICO: Erro ao inicializar cliente ENT: %v", err)
		os.Exit(1)
	}
	defer ent.CloseClient()

	// Executar migrações automáticas em ambiente de desenvolvimento
	// Apenas se não estiver em produção e se a flag de migrações estiver ativada
	if gin.Mode() != gin.ReleaseMode && os.Getenv("RUN_MIGRATIONS") == "true" {
		log.Println("Executando migrações automáticas do ENT...")
		if err := ent.RunMigrationsWithPool(context.Background()); err != nil {
			log.Printf("Aviso: Erro ao executar migrações automáticas: %v", err)
		}
	}

	// Repositórios
	// userRepo já foi inicializado acima para o middleware de autenticação
	// Estes repositórios serão substituídos por implementações ENT no futuro
	// Por enquanto, vamos manter apenas os que são usados diretamente
	notificationRepo := repository.NewGormNotificationRepository(db)

	// Repositórios serão substituídos por implementações ENT no futuro

	// Serviços
	userService := services.NewUserService(userRepo, cfg)

	// Inicializar serviço de menu centralizado
	permissionService := permissions.GetGlobalUnifiedService()
	menuService := services.NewMenuService(permissionService)

	// Função helper para adicionar menu ao contexto
	addMenuToContext := func(c *gin.Context, activePage string) gin.H {
		userValue, exists := c.Get("user")
		var user *models.User
		if exists {
			if userObj, ok := userValue.(*models.User); ok {
				user = userObj
			}
		}

		// Se não tiver user completo, criar um básico
		if user == nil {
			userID, _ := c.Get("userID")
			userRole, _ := c.Get("userRole")
			userName, _ := c.Get("userName")

			if userID != nil && userRole != nil {
				user = &models.User{
					ID:   uint(userID.(int)),
					Role: models.UserRole(userRole.(string)),
					Name: userName.(string),
				}
			}
		}

		menu := menuService.GetMenuForPage(user, activePage)

		return gin.H{
			"User":     user,
			"UserMenu": menu,
		}
	}

	// TODO: Implementar adapters para os serviços existentes
	notificationService := services.NewNotificationService(
		notificationRepo,
		userRepo,
		nil, // orderRepo será implementado com ENT no futuro
		os.Getenv("VAPID_PUBLIC_KEY"),
		os.Getenv("VAPID_PRIVATE_KEY"),
		os.Getenv("VAPID_SUBJECT"),
	)

	// Repositório e serviço de filiais
	branchRepo := repository.NewGormBranchRepository(db)
	branchService := services.NewBranchService(branchRepo)

	// Handlers
	authService = services.NewAuthService(userRepo, cfg) // Movido e corrigido para usar userRepo
	authHandler := handlers.NewAuthHandler(userService, authService)

	notificationAPIHandler := handlers.NewNotificationAPIHandler(notificationService)

	// TODO: Implementar adapters para os handlers existentes
	dashboardHandler := &handlers.DashboardHandler{}
	_ = handlers.NewWebSocketHandler(notificationService) // WebSocket handler removido temporariamente

	// Novos handlers para API
	userAPIHandler := handlers.NewUserAPIHandler(userService)
	equipmentRepo := repository.NewEquipmentRepository(db)
	equipmentService := services.NewEquipmentService(equipmentRepo)
	equipmentAPIHandler := handlers.NewEquipmentAPIHandler(equipmentService)

	// Handler para filiais
	filialFilterService := services.NewFilialFilterService(db)
	_ = handlers.NewBranchHandler(branchService, filialFilterService) // BranchHandler removido temporariamente

	// Sistema de Anexos
	attachmentRepo := repository.NewGormAttachmentRepository(db)
	attachmentService := services.NewAttachmentService(attachmentRepo)
	attachmentHandler := handlers.NewAttachmentHandler(attachmentService)

	// Sistema de Métricas por Filial
	branchMetricsService := services.NewBranchMetricsService(db)
	branchMetricsHandler := handlers.NewBranchMetricsHandler(branchMetricsService)

	// Middleware de Vinculação Automática por Filial
	branchAutoAssignmentMiddleware := middleware.NewBranchAutoAssignmentMiddleware(db)

	// Rotas públicas
	router.GET("/", func(c *gin.Context) {
		c.HTML(http.StatusOK, "geral/index.html", gin.H{
			"title": "Rede Tradição - Excelência em Serviços",
		})
	})

	router.GET("/acesso-negado", func(c *gin.Context) {
		c.HTML(http.StatusOK, "auth/acesso_negado.html", gin.H{
			"title": "Acesso Negado - Rede Tradição",
		})
	})

	router.GET("/novo-tutorial", func(c *gin.Context) {
		c.HTML(200, "tutoriais/novo_index.html", gin.H{
			"title": "Tutoriais",
		})
	})

	// Rota de gerenciamento de vínculos removida - usando apenas /admin/novo-link-management

	public := router.Group("/")
	{
		public.GET("/login", handlers.LoginHandler)
		public.POST("/api/auth/login", authHandler.Login)
		public.POST("/api/auth/logout", handlers.LogoutAPIHandler) // Usando o novo handler de logout para API
		public.GET("/logout", handlers.LogoutHandler)              // Rota de logout para acesso direto via navegador
	}

	// Rotas protegidas - Autenticação base
	protected := router.Group("/")
	protected.Use(middleware.RequireAuth())
	{
		// Grupo para páginas HTML com verificação de permissão de página
		pages := protected.Group("/")
		// Aplicar middlewares centralizados para rotas protegidas
		centralizedConfig.ApplyProtectedMiddlewares(pages)
		{
			// Tutoriais
			pages.GET("/tuto", func(c *gin.Context) {
				context := addMenuToContext(c, "tutoriais")
				context["title"] = "Tutoriais - Rede Tradição"
				context["page"] = "tutoriais"
				context["ActivePage"] = "tutoriais"

				c.HTML(http.StatusOK, "tutoriais/novo_index.html", context)
			})

			// Dashboard
			pages.GET("/dashboard", branchAutoAssignmentMiddleware.AutoAssignBranchIDForFilial(), func(c *gin.Context) {
				// Gerar token CSRF
				csrfToken := fmt.Sprintf("csrf-%d-%s", time.Now().UnixNano(), c.GetString("userID"))

				context := addMenuToContext(c, "dashboard")
				context["title"] = "Dashboard - Rede Tradição"
				context["page"] = "dashboard"
				context["ActivePage"] = "dashboard"
				context["UserRole"] = c.GetString("userRole")
				context["CSRFToken"] = csrfToken

				c.HTML(http.StatusOK, "dashboard/dashboard_new.html", context)
			})

			// Configuração das rotas de permissões
			// Nota: Usando o router principal para que as rotas sejam registradas corretamente

			// Relatórios
			pages.GET("/relatorios", func(c *gin.Context) {
				context := addMenuToContext(c, "relatorios")
				context["title"] = "Relatórios - Rede Tradição"
				context["page"] = "relatorios"
				context["ActivePage"] = "relatorios"

				c.HTML(http.StatusOK, "relatorios/reports.html", context)
			})

			// Calendário
			pages.GET("/calendario", func(c *gin.Context) {
				// Buscar todas as ordens reais do banco
				orderRepo := repository.NewMaintenanceOrderRepository(db)
				orders, _, err := orderRepo.GetAll(c, nil, 0, "admin", 1, 200)
				if err != nil {
					log.Printf("[CALENDAR-ERROR] Erro ao buscar ordens: %v", err)
					context := addMenuToContext(c, "calendario")
					context["title"] = "Calendário - Rede Tradição"
					context["page"] = "calendario"
					context["ActivePage"] = "calendario"
					context["PriorityOrders"] = []models.MaintenanceOrderDetailed{}
					context["ScheduledOrders"] = []models.MaintenanceOrderDetailed{}
					context["Error"] = "Erro ao buscar ordens de manutenção: " + err.Error()

					c.HTML(500, "calendarios/calendar_flip.html", context)
					return
				}
				// Filtrar ordens prioritárias (aguardando, atrasado, urgente)
				var priorityOrders []models.MaintenanceOrderDetailed
				var scheduledOrders []models.MaintenanceOrderDetailed
				for _, o := range orders {
					status := o.Status
					priority := o.Priority
					if status == "pending" || status == "delayed" || priority == "critical" || priority == "alta" || priority == "urgente" {
						priorityOrders = append(priorityOrders, o)
					} else if status == "approved" || status == "completed" || status == "scheduled" || status == "aprovada" || status == "concluida" || status == "agendada" {
						scheduledOrders = append(scheduledOrders, o)
					}
				}
				c.HTML(http.StatusOK, "calendarios/calendar_flip.html", gin.H{
					"title":           "Calendário - Rede Tradição",
					"page":            "calendario",
					"ActivePage":      "calendario",
					"PriorityOrders":  priorityOrders,
					"ScheduledOrders": scheduledOrders,
				})
			})

			// Rota para Ordens Técnicas (página específica para técnicos)
			// Rota removida

			// Minha Conta
			pages.GET("/minha-conta", func(c *gin.Context) {
				context := addMenuToContext(c, "minha-conta")
				context["title"] = "Minha Conta - Rede Tradição"
				context["page"] = "minha-conta"
				context["ActivePage"] = "minha-conta"
				context["now"] = time.Now()

				// Adicionar dados do usuário se não estiverem no contexto
				if context["User"] == nil {
					context["User"] = gin.H{
						"ID":    c.GetInt("userID"),
						"Name":  c.GetString("userName"),
						"Email": c.GetString("userEmail"),
						"Role":  c.GetString("userRole"),
					}
				}

				c.HTML(http.StatusOK, "minhaconta/minha_conta.html", context)
			})

			// Minha Filial - Exclusiva para usuários do tipo filial
			// Rota movida para web/routes/minha_filial_routes.go para evitar conflitos

			// Editar Perfil
			pages.GET("/editar-perfil", func(c *gin.Context) {
				context := addMenuToContext(c, "minha-conta")
				context["title"] = "Editar Perfil - Rede Tradição"
				context["page"] = "editar-perfil"
				context["ActivePage"] = "minha-conta"

				// Adicionar dados do usuário se não estiverem no contexto
				if context["User"] == nil {
					context["User"] = gin.H{
						"ID":       c.GetInt("userID"),
						"Name":     c.GetString("userName"),
						"Email":    c.GetString("userEmail"),
						"Role":     c.GetString("userRole"),
						"Phone":    c.GetString("userPhone"),
						"Position": c.GetString("userPosition"),
						"Bio":      c.GetString("userBio"),
						"Preferences": gin.H{
							"EmailNotifications": true,
							"PushNotifications":  false,
							"SmsNotifications":   false,
						},
					}
				}

				c.HTML(http.StatusOK, "minhaconta/editar_perfil.html", context)
			})

			// Alterar Senha (página)
			pages.GET("/minha-conta/alterar-senha", func(c *gin.Context) {
				// Gerar token CSRF
				csrfToken := "token-simulado-123456" // Em produção, use um gerador de token real

				context := addMenuToContext(c, "minha-conta")
				context["title"] = "Alterar Senha - Rede Tradição"
				context["page"] = "alterar-senha"
				context["ActivePage"] = "minha-conta"
				context["csrfToken"] = csrfToken

				// Adicionar dados do usuário se não estiverem no contexto
				if context["User"] == nil {
					context["User"] = gin.H{
						"ID":    c.GetInt("userID"),
						"Name":  c.GetString("userName"),
						"Email": c.GetString("userEmail"),
						"Role":  c.GetString("userRole"),
					}
				}

				c.HTML(http.StatusOK, "minhaconta/alterar_senha.html", context)
			})

			// Configurações de Segurança (página)
			pages.GET("/security-settings", func(c *gin.Context) {
				context := addMenuToContext(c, "security")
				context["title"] = "Configurações de Segurança - Rede Tradição"
				context["page"] = "security"
				context["ActivePage"] = "security"

				c.HTML(http.StatusOK, "auth/security_settings.html", context)
			})

			// Alterar Senha (página alternativa)
			pages.GET("/change-password", func(c *gin.Context) {
				context := addMenuToContext(c, "change-password")
				context["title"] = "Alterar Senha - Rede Tradição"
				context["page"] = "change-password"
				context["ActivePage"] = "change-password"

				c.HTML(http.StatusOK, "auth/change_password.html", context)
			})

			// Rota direta para calendario-flip (precisa de PageAccessMiddleware)
			pages.GET("/calendario-flip", func(c *gin.Context) {
				db := c.MustGet("db").(*gorm.DB)
				userIDStr := c.GetString("userID")
				userRole := c.GetString("userRole")
				userID, err := strconv.Atoi(userIDStr)
				if err != nil {
					c.HTML(http.StatusInternalServerError, "calendarios/calendar_flip.html", gin.H{
						"title":      "Calendário Flip - Rede Tradição",
						"page":       "calendario-flip",
						"ActivePage": "calendario-flip",
						"error":      "Falha ao obter dados de usuário",
					})
					return
				}

				// Preparar filtros baseados no perfil do usuário
				filters := map[string]interface{}{}
				// Admins, gerentes e financeiro podem ver todas as ordens sem filtros
				if userRole != "admin" && userRole != "gerente" && userRole != "financeiro" {
					// Buscar equipamentos do técnico/prestador
					var techEquipments []models.TechnicianEquipment
					if err := db.Where("user_id = ?", userID).Find(&techEquipments).Error; err != nil {
						c.HTML(http.StatusInternalServerError, "calendarios/calendar_flip.html", gin.H{
							"title":      "Calendário Flip - Rede Tradição",
							"page":       "calendario-flip",
							"ActivePage": "calendario-flip",
							"error":      "Falha ao buscar equipamentos do técnico",
						})
						return
					}

					equipmentTypes := make([]string, 0, len(techEquipments))
					for _, te := range techEquipments {
						equipmentTypes = append(equipmentTypes, te.EquipmentType)
					}

					var user models.User
					if err := db.First(&user, userID).Error; err != nil {
						c.HTML(http.StatusInternalServerError, "calendarios/calendar_flip.html", gin.H{
							"title":      "Calendário Flip - Rede Tradição",
							"page":       "calendario-flip",
							"ActivePage": "calendario-flip",
							"error":      "Falha ao buscar dados do usuário",
						})
						return
					}

					filters["branch_id"] = user.BranchID
					if len(equipmentTypes) > 0 {
						filters["equipment_type"] = equipmentTypes
					}
				} else {
					log.Printf("[INFO] Usuário %s (ID: %d) com perfil %s acessando todas as ordens sem filtros",
						c.GetString("userEmail"), userID, userRole)
				}

				// Buscar ordens usando o repositório
				repo := repository.NewMaintenanceOrderRepository(db)
				allOrders, _, err := repo.GetAll(c, filters, int64(userID), userRole, 1, 200)
				if err != nil {
					c.HTML(http.StatusInternalServerError, "calendarios/calendar_flip.html", gin.H{
						"title":      "Calendário Flip - Rede Tradição",
						"page":       "calendario-flip",
						"ActivePage": "calendario-flip",
						"error":      "Falha ao buscar ordens de manutenção: " + err.Error(),
					})
					return
				}

				// Dividir em ordens prioritárias e programadas
				priorityOrders := []models.MaintenanceOrderDetailed{}
				scheduledOrders := []models.MaintenanceOrderDetailed{}

				for _, order := range allOrders {
					switch order.Status {
					case "aguardando", "atrasado", "urgente":
						priorityOrders = append(priorityOrders, order)
					case "aprovado", "concluido", "programado":
						scheduledOrders = append(scheduledOrders, order)
					}
				}

				log.Printf("[INFO] Encontradas %d ordens prioritárias e %d ordens programadas para o usuário %s",
					len(priorityOrders), len(scheduledOrders), c.GetString("userEmail"))

				context := addMenuToContext(c, "calendario-flip")
				context["title"] = "Calendário Flip - Rede Tradição"
				context["page"] = "calendario-flip"
				context["ActivePage"] = "calendario-flip"
				context["PriorityOrders"] = priorityOrders
				context["ScheduledOrders"] = scheduledOrders

				// Adicionar dados do usuário se não estiverem no contexto
				if context["User"] == nil {
					context["User"] = gin.H{
						"ID":    userID,
						"Name":  c.GetString("userName"),
						"Email": c.GetString("userEmail"),
						"Role":  userRole,
					}
				}

				c.HTML(http.StatusOK, "calendarios/calendar_flip.html", context)
			})

			// Configurar rotas da galeria (usar router principal, PageAccessMiddleware aplicado dentro da função ou nas rotas específicas)
			// routes.SetupGaleriaRoutes(pages) // Correção: Passar router
			// A lógica de permissão específica (página vs API) deve ser tratada dentro de SetupGaleriaRoutes

			// Página de Gerenciamento de Relações (apenas admin, gerente e financeiro)
			adminPages := pages.Group("")
			adminPages.Use(middleware.RoleMiddleware("admin", "gerente", "financeiro"))
			// (rota antiga de gerenciamento de vínculos removida)

			pages.GET("/tutorial/ordens", func(c *gin.Context) {
				context := addMenuToContext(c, "tutorial-ordens")
				context["title"] = "Tutorial Interativo: Criar Ordem de Manutenção"
				context["page"] = "tutorial-ordens"
				context["ActivePage"] = "tutorial-ordens"

				c.HTML(http.StatusOK, "tutorials/ordens_tutorial_interativo.html", context)
			})

			// Dentro do grupo pages (rotas protegidas)
			// Remover a rota antiga de gerenciamento de vínculos

			// Após a definição do grupo 'pages' (rotas protegidas)
			webRoutes.SetupFinanceiroCalculadoraRoutes(pages)

			protected.GET("/tutoriais/ordem_manutencao_visual", func(c *gin.Context) {
				c.HTML(200, "tutoriais/ordem_manutencao_visual.html", gin.H{"title": "Tutorial Visual - Ordem de Manutenção"})
			})
			protected.GET("/tutoriais/ordem_manutencao_interativo", func(c *gin.Context) {
				c.HTML(200, "tutoriais/ordem_manutencao_interativo.html", gin.H{"title": "Tutorial Interativo - Ordem de Manutenção"})
			})
			protected.GET("/tutoriais/novo_index", func(c *gin.Context) {
				c.HTML(200, "tutoriais/novo_index.html", gin.H{"title": "Tutoriais"})
			})
			protected.GET("/tutoriais/ordem_manutencao_real", func(c *gin.Context) {
				c.HTML(200, "tutoriais/ordem_manutencao_real.html", gin.H{"title": "Tutorial Real - Ordem de Manutenção"})
			})
			// Rota painelvinculos removida - usando apenas /admin/novo-link-management

		} // Fim do grupo 'pages'

		// API Routes com verificação de permissão de API
		api := protected.Group("/api")
		// Aplicar middlewares centralizados para rotas de API
		centralizedConfig.ApplyAPIMiddlewares(api)
		{
			// Rotas de autenticação protegidas
			auth := api.Group("/auth")
			{
				auth.GET("/me", authHandler.GetCurrentUser)
				auth.GET("/2fa/setup", authHandler.SetupTOTP)
				auth.POST("/2fa/enable", authHandler.VerifyAndEnableTOTP)
				auth.POST("/2fa/disable", authHandler.DisableTOTP)
			}

			// Rotas de usuário
			user := api.Group("/user")
			{
				user.GET("/me", userAPIHandler.GetCurrentUser)
				user.PUT("/profile", userAPIHandler.UpdateUserProfile)
				user.POST("/change-password", userAPIHandler.ChangePassword)
				user.POST("/avatar", handlers.UploadAvatar)
			}

			// Rota para técnicos (para o calendário)
			api.GET("/users/technicians", userAPIHandler.GetTechnicians)

			// Rotas de equipamentos
			equipments := api.Group("/equipments")
			equipments.Use(middleware.FilialMiddleware()) // Middleware específico para equipamentos
			{
				equipments.GET("", equipmentAPIHandler.GetAllEquipments)
				equipments.GET("/:id", equipmentAPIHandler.GetEquipmentByID)
				equipments.POST("", equipmentAPIHandler.CreateEquipment)
				equipments.PUT("/:id", equipmentAPIHandler.UpdateEquipment)
				equipments.DELETE("/:id", equipmentAPIHandler.DeleteEquipment)
				equipments.GET("/types", equipmentAPIHandler.GetEquipmentTypes)
				equipments.GET("/filial/:id", equipmentAPIHandler.GetEquipmentsByFilial)
			}

			// Rotas de métricas por filial
			branches := api.Group("/branches")
			branches.Use(branchAutoAssignmentMiddleware.BranchFilterMiddleware()) // Aplicar filtro por filial
			{
				branches.GET("/metrics", branchMetricsHandler.GetAllBranchesMetrics)
				branches.GET("/:id/metrics", branchMetricsHandler.GetBranchMetrics)
				branches.GET("/:id/equipment-metrics", branchMetricsHandler.GetEquipmentTypeMetrics)
			}

			// Rotas de métricas da filial do usuário
			myBranch := api.Group("/my-branch")
			myBranch.Use(branchAutoAssignmentMiddleware.BranchFilterMiddleware())
			{
				myBranch.GET("/metrics", branchMetricsHandler.GetMyBranchMetrics)
				myBranch.GET("/equipment-metrics", branchMetricsHandler.GetMyBranchEquipmentMetrics)
			}

			// Rotas de equipamentos problemáticos (apenas admin)
			api.GET("/equipment/problematic", branchMetricsHandler.GetTopProblematicEquipment)

			// Rota adicional para compatibilidade com o frontend
			equipment := api.Group("/equipment")
			equipment.Use(middleware.FilialMiddleware())
			{
				equipment.GET("/types", equipmentAPIHandler.GetEquipmentTypes)
			}

			// Rota específica para tipos de equipamento removida - agora configurada via SetupEquipmentTypeRoutes

			// Inicializar serviço e handler de transferências de equipamentos
			transferRepo := repository.NewEquipmentTransferRepository(db)
			transferService := services.NewEquipmentTransferService(transferRepo, equipmentRepo, branchRepo, notificationService)
			transferHandler := handlers.NewEquipmentTransferHandler(transferService)

			// Configurar rotas de transferência de equipamentos (API - usar router principal)
			routes.SetupEquipmentTransferRoutes(router, transferHandler)

			// Inicializar handler de notificações API (usar router principal)
			// Mover declaração para fora do grupo 'api'
			// notificationAPIHandler := handlers.NewNotificationAPIHandler(notificationService)
			notificationAPIHandler.RegisterRoutes(router, authService.(*services.AuthService), db)

			// Rotas de filiais (API) são configuradas em routes.SetupBranchRoutes

			// Rotas da Minha Filial movidas para web/routes/minha_filial_routes.go

			// Rotas de manutenção (API)
			maintenance := api.Group("/maintenance")
			maintenance.Use(branchAutoAssignmentMiddleware.BranchFilterMiddleware()) // Aplicar filtro por filial
			{
				maintenance.GET("", func(c *gin.Context) {
					c.JSON(http.StatusNotImplemented, gin.H{
						"success": false,
						"message": "Funcionalidade movida para /api/orders - usar unified handler",
					})
				})
				// Aplicar vinculação automática na criação de ordens
				maintenance.POST("", branchAutoAssignmentMiddleware.AutoAssignBranch(), func(c *gin.Context) {
					c.JSON(http.StatusNotImplemented, gin.H{
						"success": false,
						"message": "Funcionalidade movida para /api/orders - usar unified handler",
					})
				})
			}
			api.GET("/dashboard/metrics", dashboardHandler.GetMetrics)

			// Rotas de ordens de manutenção (API) - REMOVIDAS
			// Funcionalidade consolidada no unified_order_handler
			// Usar /api/orders em vez de /api/maintenance-orders
			orders := api.Group("/maintenance-orders")
			{
				// Redirecionamentos para nova arquitetura
				orders.GET("", func(c *gin.Context) {
					c.Redirect(http.StatusMovedPermanently, "/api/orders")
				})
				orders.GET("/metrics", func(c *gin.Context) {
					c.Redirect(http.StatusMovedPermanently, "/api/orders/metrics")
				})
				orders.POST("/remove-test-orders", func(c *gin.Context) {
					c.JSON(http.StatusNotImplemented, gin.H{
						"success": false,
						"message": "Funcionalidade movida para /api/orders - usar unified handler",
					})
				})
			} // Fim do grupo 'orders' dentro de 'api'

			// Rotas para gerenciar atribuições de ordens a técnicos - REMOVIDAS
			// Funcionalidade consolidada no unified_order_handler
			techOrders := api.Group("/technician-orders")
			{
				// Redirecionamentos para nova arquitetura
				techOrders.POST("/assign", func(c *gin.Context) {
					c.JSON(http.StatusNotImplemented, gin.H{
						"success": false,
						"message": "Funcionalidade movida para /api/orders - usar unified handler",
					})
				})

				techOrders.DELETE("/technician/:technicianId/order/:orderId", func(c *gin.Context) {
					c.JSON(http.StatusNotImplemented, gin.H{
						"success": false,
						"message": "Funcionalidade movida para /api/orders - usar unified handler",
					})
				})

				// Redirecionar para endpoint unificado
				techOrders.GET("/technician/:technicianId", func(c *gin.Context) {
					c.Redirect(http.StatusMovedPermanently, "/api/orders/technician")
				})

				techOrders.GET("/order/:orderId", func(c *gin.Context) {
					c.Redirect(http.StatusMovedPermanently, "/api/orders/"+c.Param("orderId"))
				})

				techOrders.POST("/batch-assign", func(c *gin.Context) {
					c.JSON(http.StatusNotImplemented, gin.H{
						"success": false,
						"message": "Funcionalidade movida para /api/orders - usar unified handler",
					})
				})
			}

			// Configurar rotas UNIFICADAS de ordens de serviço - API
			// SUBSTITUINDO todas as rotas duplicadas por endpoints únicos e consistentes
			// NOTA: As rotas unificadas são configuradas via SetupUnifiedOrderRoutesNew() mais abaixo
			// para evitar duplicação e manter consistência

		} // Fim do grupo 'api'

		// ROTAS WEB - Páginas HTML (fora do grupo API)
		// NOTA: As rotas web de ordens são configuradas via SetupUnifiedOrderRoutesNew()
		// para evitar duplicação e manter consistência

	} // Fim do grupo 'protected'

	// Remover rotas duplicadas ou movidas que estavam fora do grupo 'protected' ou 'pages'
	// As rotas /calendario-flip foram movidas para dentro do grupo 'pages'.
	// As chamadas para Setup...Routes foram ajustadas para usar o 'router' principal.

	// Configurar rotas da galeria com filtro de permissões (usar router principal)
	// Usar o serviço de filiais existente
	routes.SetupFilialFilteredRoutes(router, branchService)

	// Configurar rotas de prestadoras (usar router principal)
	routes.SetupPrestadorasRoutes(router)

	// Configurar rotas de técnicos
	technicianRepo := repository.NewTechnicianRepository(db)
	technicianService := services.NewTechnicianService(technicianRepo)
	technicianHandler := handlers.NewTechnicianHandler(userService, technicianService)
	routes.SetupTechnicianRoutes(router, technicianHandler)

	// Configurar rotas de especialidades de técnico
	techSpecialtyService := services.NewTechnicianSpecialtyService(db)
	auditService := services.NewAuditService(db)
	techSpecialtyHandler := handlers.NewTechnicianSpecialtyHandler(techSpecialtyService, auditService)
	routes.SetupTechnicianSpecialtyRoutes(router, techSpecialtyHandler)

	// Configurar rotas de convites de técnicos
	technicianInviteRepo := repository.NewTechnicianInviteRepository(db)
	technicianInviteService := services.NewTechnicianInviteService(technicianInviteRepo)
	technicianInviteHandler := handlers.NewTechnicianInviteHandler(technicianInviteService, userService)
	routes.SetupTechnicianInviteRoutes(router, technicianInviteHandler, authService.(*services.AuthService), db)

	// Configurar rotas de tipos de equipamento
	routes.SetupEquipmentTypeRoutes(router)

	// Configurar rotas de usuários (criação, edição, listagem)
	routes.RegisterUserRoutes(router)

	// REMOVIDO: Rotas de permissões temporariamente desabilitadas durante consolidação
	// routes.SetupPermissionRoutes(router, permissionsService, "data/permissions.yaml")

	// Configurar sistema de gerenciamento de vínculos
	serviceProviderRepo := repository.NewServiceProviderRepository(db)
	// Reutilizar o repositório de técnicos já criado acima

	// Criar uma nova instância do repositório de filiais
	branchRepoImpl := repository.NewBranchRepository(db)
	branchRepoAdapter := repository.NewBranchRepositoryAdapter(branchRepoImpl)

	// Inicializar repositório de gestores de prestadores
	serviceProviderManagerRepo := repository.NewGormServiceProviderManagerRepository()

	// Inicializar serviço de upload de arquivos (necessário para o ServiceProviderService)
	uploadService := services.NewFileUploadService("uploads")

	// Inicializar serviço de email (necessário para o ServiceProviderService)
	// Usando um serviço de email simulado para desenvolvimento
	emailService := services.NewSimulatedEmailService()

	// Inicializar o serviço de prestadores
	serviceProviderService := services.NewServiceProviderService(
		serviceProviderRepo,
		userRepo,
		serviceProviderManagerRepo,
		emailService,
		uploadService,
	)

	// Inicializar o handler de prestadores
	serviceProviderHandler := handlers.NewServiceProviderHandler(
		serviceProviderService,
		userService,
		uploadService,
		emailService,
	)

	// Configurar rotas de prestadores
	routes.SetupServiceProviderRoutes(router, serviceProviderHandler)

	// Inicializar serviço de gerenciamento de vínculos
	linkManagementService := services.NewLinkManagementService(
		db,
		branchRepoAdapter,
		technicianRepo,
		serviceProviderRepo,
	)
	linkManagementController := controllers.NewLinkManagementController(linkManagementService)
	routes.SetupLinkManagementRoutes(router, linkManagementController, nil)

	// Remover ou comentar a linha abaixo, pois o módulo foi removido
	// routes.SetupNovoLinkManagementRoutes(router)
	routes.SetupNovoLinkManagementRoutes(router)

	// Configurar repositório e serviço de ordens
	ordemRepo := repository.NewOrdemRepository(db)
	ordemService := services.NewOrdemService(ordemRepo)
	ordemV2Handler := handlers.NewOrdemV2Handler(ordemService)
	routes.SetupOrdensRoutes(router, ordemV2Handler)
	// NOTA: SetupUnifiedOrderRoutesNew() é chamado mais abaixo para evitar duplicação

	// Configurar rotas de anexos
	routes.SetupAttachmentRoutes(router, attachmentHandler)
	routes.SetupLegacyAttachmentRoutes(router, attachmentHandler)

	// Configurar rotas unificadas de ordens (API /api/orders)
	// NOTA: Esta função configura todas as rotas unificadas de ordens
	// incluindo API e páginas web, substituindo todas as rotas duplicadas
	routes.SetupUnifiedOrderRoutesNew(router)

	// Configurar rotas de tutoriais (deve ser registrada após as rotas de ordens)
	// Remover qualquer linha como:
	// routes.SetupTutorialRoutes(router, db)

	// Configurar rotas da minha filial
	webRoutes.SetupRoutes(router, db)

	// Configurar componentes unificados (inclui rotas /api/branches)
	setup.SetupUnifiedComponents(router, db)

	// Registrar funções de template personalizadas
	for nome, funcao := range templates.TemplateFuncs() {
		router.SetFuncMap(template.FuncMap{
			nome: funcao,
		})
	}

	// Configurar rotas de verificação de ordens
	routes.SetupVerOrdensRoutes(router)

	// === ROTAS DE CONVITE DE PRESTADORA ===
	providerInviteService := services.NewProviderInviteService(db, emailService)
	providerRepo := repository.NewServiceProviderRepository(db)
	providerService := services.NewServiceProviderService(providerRepo, userRepo, nil, nil, nil)
	providerInviteHandler := handlers.NewProviderInviteHandler(providerInviteService, providerService)
	router.POST("/api/provider-invites", providerInviteHandler.CreateInvite)
	router.GET("/api/provider-invites/validate/:token", providerInviteHandler.ValidateToken)
	router.POST("/api/provider-invites/register", providerInviteHandler.RegisterProvider)
	router.GET("/register/provider", func(c *gin.Context) {
		token := c.Query("token")
		if token == "" {
			c.HTML(400, "error.html", gin.H{
				"title":   "Erro",
				"message": "Token de convite não fornecido",
			})
			return
		}
		c.HTML(200, "prestadora/register_provider.html", gin.H{
			"title": "Cadastro de Prestadora",
			"token": token,
		})
	})

	// Registrar endpoint /metrics para Prometheus
	router.GET("/metrics", gin.WrapH(promhttp.Handler()))

	// Configurar tratamento de sinais para encerramento gracioso
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)

	// Iniciar o servidor em uma goroutine separada
	// Sempre usar a porta 8080, independentemente da configuração
	addr := fmt.Sprintf("0.0.0.0:%d", 8080)
	log.Printf("Servidor iniciado em %s (http://%s)", addr, addr)

	// Configurar servidor HTTPS
	srv := &http.Server{
		Addr:    addr,
		Handler: router,
	}

	// Iniciar o servidor em uma goroutine
	go func() {
		if err := srv.ListenAndServeTLS("cert.pem", "key.pem"); err != nil && err != http.ErrServerClosed {
			log.Printf("ERRO CRÍTICO: Erro ao iniciar servidor HTTPS: %v", err)
			os.Exit(1)
		}
	}()

	log.Printf("Servidor HTTPS iniciado em https://%s", addr)

	// Aguardar sinal de interrupção
	<-quit
	log.Println("Encerrando servidor...")

	// Criar contexto com timeout para shutdown
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// Tentar encerrar o servidor graciosamente
	if err := srv.Shutdown(ctx); err != nil {
		log.Printf("ERRO CRÍTICO: Erro ao encerrar servidor: %v", err)
		os.Exit(1)
	}

	log.Println("Servidor encerrado com sucesso")
}
