#!/bin/bash

# Carregar o perfil para garantir que o PATH do Go esteja disponível
source /etc/profile

# Cores para saída
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
CYAN='\033[0;36m'
NC='\033[0m' # No Color

clear

# Banner inicial
cat <<EOF
${CYAN}==============================================${NC}
   ${GREEN}INICIALIZAÇÃO RÁPIDA - REDE TRADIÇÃO${NC}
${CYAN}==============================================${NC}
EOF

echo -e "${CYAN}[$(date '+%Y-%m-%d %H:%M:%S')]${NC} Iniciando servidor...\n"

# Verificar diretório raiz
if [ ! -d "web/templates" ]; then
    if [ "$(basename $(pwd))" = "cmd" ]; then
        echo -e "${YELLOW}[!] Diretório cmd detectado, voltando para raiz${NC}"
        cd ..
    fi
    if [ ! -d "web/templates" ]; then
        echo -e "${RED}[✗] Erro: Diretório web/templates não encontrado!${NC}"
        exit 1
    fi
fi

echo -e "${GREEN}[✓] Diretório de trabalho: $(pwd)${NC}"

# Carregar variáveis de ambiente
if [ -f .env ]; then
    echo -e "${GREEN}[✓] .env carregado${NC}"
    source .env
else
    echo -e "${RED}[✗] .env não encontrado! Configure antes de iniciar.${NC}"
    exit 1
fi

# Checar variáveis obrigatórias
if [ -z "$DB_HOST" ] || [ -z "$DB_PORT" ] || [ -z "$DB_USER" ] || [ -z "$DB_PASS" ] || [ -z "$DB_NAME" ]; then
    echo -e "${RED}[✗] Variáveis obrigatórias ausentes no .env${NC}"
    exit 1
fi

export DB_PERSISTENT_CONNECTION="true"
export GIN_MODE="release"
export HOST="0.0.0.0"
export PORT="8080"

# Liberar portas
cat <<EOF
${YELLOW}----------------------------------------------${NC}
${CYAN}Verificando e liberando portas necessárias...${NC}
${YELLOW}----------------------------------------------${NC}
EOF
encerrar_processo_na_porta() {
    local porta=$1
    if netstat -tuln | grep -q ":$porta "; then
        local PID=$(netstat -tlnp 2>/dev/null | grep ":$porta" | awk '{print $7}' | cut -d'/' -f1)
        if [ ! -z "$PID" ]; then
            kill -9 $PID 2>/dev/null
            sleep 1
        fi
    fi
}
encerrar_processo_na_porta 8080
encerrar_processo_na_porta 8083
encerrar_processo_na_porta 8082
if netstat -tuln | grep -q ":8080 "; then
    echo -e "${RED}[✗] Porta 8080 ocupada. Encerre o processo manualmente.${NC}"
    exit 1
fi

echo -e "${GREEN}[✓] Banco de dados remoto configurado${NC}"
echo -e "${GREEN}[✓] Servidor na porta $PORT${NC}\n"

# Finalizar processos Go antigos
if pgrep -f "go run cmd/main.go" > /dev/null; then
    pkill -f "go run cmd/main.go" || true
    pkill -f "/tmp/go-build" || true
    sleep 2
fi

cat <<EOF
${CYAN}----------------------------------------------${NC}
${GREEN}Compilando e iniciando aplicação...${NC}
${CYAN}----------------------------------------------${NC}
EOF

BUILD_ERRORS=$(go build ./... 2>&1 | tee build_errors.log)
if [ $? -eq 0 ]; then
    echo -e "${GREEN}[✓] Compilação dos pacotes concluída${NC}"
    echo -e "${CYAN}[$(date '+%H:%M:%S')] Compilando app principal...${NC}"
    go build -buildvcs=false -o app cmd/main.go > build_errors.log 2>&1
    if [ $? -eq 0 ]; then
        echo -e "${GREEN}[✓] App compilado com sucesso${NC}"
        chmod +x app
        echo -e "${CYAN}[$(date '+%H:%M:%S')] Iniciando servidor...${NC}"
        echo -e "\n${YELLOW}==============================================${NC}"
        echo -e "${GREEN}Acesse: http://localhost:$PORT${NC}"
        echo -e "${YELLOW}==============================================${NC}\n"
        ./app
    else
        echo -e "${RED}[✗] Erro ao compilar o app principal. Veja build_errors.log${NC}"
        cat build_errors.log
        exit 1
    fi
else
    echo -e "${RED}[✗] Erros de compilação:${NC}"
    echo "$BUILD_ERRORS"
    exit 1
fi
