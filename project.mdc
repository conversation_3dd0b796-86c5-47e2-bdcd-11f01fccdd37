---
description: Regras específicas do projeto Tradição - Arquitetura, padrões e regras de desenvolvimento
globs:
alwaysApply: true
---
# 🏗️ PROJETO TRADIÇÃO - REGRAS ESPECÍFICAS
## Arquitetura, Padrões e Desenvolvimento

> **OBRIGATÓRIO**: Consultar `docs/REGRAS_DESENVOLVIMENTO.md` primeiro (documento conciso)

---

## 🏛️ **ARQUITETURA PRINCIPAL**

### **Sistema Base**
- Sistema Go/Gin com arquitetura unificada
- Sistema de permissões centralizado em `internal/permissions/`
- Middlewares centralizados em `internal/middleware/`
- Documentação principal em `docs/REGRAS_UNIFICADAS.md`
- Configuração de permissões em `data/permissions.yaml`

### **Arquivos Críticos**
- `cmd/main.go`: Ponto de entrada e inicialização de permissões
- `internal/permissions/unified_service.go`: Serviço de permissões
- `internal/middleware/centralized_config.go`: Middlewares globais
- `data/permissions.yaml`: Configuração de permissões
- `docs/REGRAS_UNIFICADAS.md`: Fonte única da verdade

---

## 📝 **PADRÕES OBRIGATÓRIOS**

### **Nomenclatura**
- Código em inglês, interface em português
- APIs em snake_case, modelos em PascalCase
- Sistema de permissões obrigatório para todas as rotas
- Middlewares centralizados aplicados automaticamente
- Compatibilidade com código legado (rotas, papéis, modelos)

### **Papéis de Usuário Padronizados**
- admin, gerente, financeiro, filial, technician, provider
- Suporte a aliases legados: tecnico, prestador, branch_user

### **APIs Principais**
- `/api/unified/filiais` (recomendado)
- `/api/orders` (ordens unificadas)
- `/api/branches` (compatibilidade)

---

## 🔐 **SISTEMA DE PERMISSÕES CENTRALIZADO**

### **Regras Obrigatórias**
- **OBRIGATÓRIO**: Inicialização no `main.go`
- **OBRIGATÓRIO**: Middleware em todas as rotas
- **OBRIGATÓRIO**: Verificação em handlers
- **NUNCA** usar valores hardcoded em permissões
- **SEMPRE** usar `data/permissions.yaml` para configurações

### **Centralização de Regras**
- Toda lógica de autenticação, permissão e negócio deve ser centralizada em middlewares e serviços unificados
- Não duplicar lógica de permissão/autenticação em múltiplos pontos
- Seguir `docs/CENTRALIZACAO_REGRAS.md` para implementação

---

## 🚨 **REGRAS ABSOLUTAS DA FASE 1**

### **1. PROIBIÇÃO TOTAL DE HARDCODED**
- **NUNCA** usar valores hardcoded em permissões, status, tipos ou papéis
- **SEMPRE** usar `data/permissions.yaml` para configurações
- **SEMPRE** usar sistema centralizado para permissões

### **2. TRATAMENTO DE ERROS OBRIGATÓRIO**
- **SEMPRE** tratar erros adequadamente
- **SEMPRE** prevenir panics com `defer recover()`
- **SEMPRE** implementar fallbacks seguros

### **3. LOGS ESTRUTURADOS OBRIGATÓRIOS**
- **OBRIGATÓRIO**: Logs de auditoria para operações críticas
- **OBRIGATÓRIO**: Logs de erro informativos
- **OBRIGATÓRIO**: Logs de acesso estruturados

### **4. CÓDIGO LIMPO CONTEXTUAL**
- **BACKEND**: Comentários informativos permitidos
- **FRONTEND**: Apenas comentários de estrutura/funcionalidade
- **PROIBIDO**: Informações sensíveis no frontend
- **PROIBIDO**: Código obsoleto comentado

---

## 📋 **CHECKLISTS OBRIGATÓRIOS**

### **Antes de Commit**
- [ ] 0 valores hardcoded em permissões
- [ ] Sistema de permissões inicializado no main.go
- [ ] Todos os erros tratados adequadamente
- [ ] Logs de auditoria implementados
- [ ] Comentários informativos (backend) / Sem informações sensíveis (frontend)

### **Implementação**
- [ ] Verificar se funcionalidade já existe
- [ ] Seguir convenções de nomenclatura
- [ ] Implementar sistema de permissões
- [ ] Adicionar logs de auditoria
- [ ] Testar responsividade e acessibilidade
- [ ] **OBRIGATÓRIO**: Seguir checklist da Fase 1

---

## 🔧 **REGRAS DE DESENVOLVIMENTO**

### **Principais**
- **OBRIGATÓRIO**: Consultar `docs/REGRAS_DESENVOLVIMENTO.md` primeiro (documento conciso)
- **OBRIGATÓRIO**: Seguir as 5 regras absolutas da Fase 1
- Não duplicar funcionalidades existentes
- Seguir sistema de permissões unificado
- Manter compatibilidade com código legado
- Toda lógica de permissão/autenticação deve ser centralizada

### **Observações**
- Sempre respeitar as regras de centralização de regras e permissões
- Não alterar lógica de backend fora dos pontos centralizados
- Documentação e exemplos sempre em `docs/`

---

## 📚 **DOCUMENTOS DE REFERÊNCIA**

### **Principais**
- `docs/REGRAS_DESENVOLVIMENTO.md` - **DOCUMENTO CONCISO** (5 regras principais)
- `docs/REGRAS_UNIFICADAS.md` - Documento completo
- `docs/CENTRALIZACAO_REGRAS.md` - Regras de centralização
- `docs/REGRAS_EMERGENTES_FASE1.md` - Regras detalhadas da Fase 1

### **Banco de Dados**
```
DB_USER=postgres
DB_PASS=i1t2a3l4o5
DB_HOST=*************
DB_PORT=5432
DB_NAME=tradicao
```

---

## 🚨 **REGRA ABSOLUTA**

- **PROIBIDO** hipóteses, achismos e suposições.
- **SÓ** responder com fatos comprovados por código, banco ou documentação oficial do projeto.

---

**Essas regras são específicas do projeto Tradição e devem ser seguidas em conjunto com as regras gerais.**


## REGRA ABSOLUTA
- PROIBIDO hipóteses, achismos e suposições.
- Só responder com fatos comprovados por código, banco ou documentação oficial do projeto.
